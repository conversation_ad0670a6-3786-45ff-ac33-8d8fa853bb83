# =============================================================================
# SISTEMA DE ALERTAS YAHOO FINANCE - MONITOREO AUTOMÁTICO
# =============================================================================
# Sistema de alertas automáticas con sonidos y notificaciones
# =============================================================================

library(quantmod)
library(TTR)

# Intentar cargar paquetes de alertas
alertas_disponibles <- list(
  sonoras = FALSE,
  visuales = FALSE
)

# Verificar beepr para sonidos
if (require(beepr, quietly = TRUE)) {
  alertas_disponibles$sonoras <- TRUE
}

# Verificar tcltk para ventanas
if (require(tcltk, quietly = TRUE)) {
  alertas_disponibles$visuales <- TRUE
}

cat("🚨 SISTEMA DE ALERTAS YAHOO FINANCE\n")
cat("===================================\n")
cat("Monitoreo automático con alertas\n")
cat("Alertas sonoras:", ifelse(alertas_disponibles$sonoras, "✅", "❌"), "\n")
cat("Alertas visuales:", ifelse(alertas_disponibles$visuales, "✅", "❌"), "\n\n")

# =============================================================================
# CONFIGURACIÓN DE ALERTAS
# =============================================================================

ALERTAS_CONFIG <- list(
  # Configuración de monitoreo
  intervalo_minutos = 30,           # Revisar cada 30 minutos
  max_ciclos = 48,                  # 24 horas de monitoreo
  
  # Filtros de alertas
  fuerza_minima = 3,                # Mínimo 3 de fuerza
  confirmaciones_minimas = 2,       # Mínimo 2 confirmaciones
  
  # Configuración de archivos
  archivo_alertas = "logs/alertas_scalping.txt",
  archivo_log = "logs/monitoreo_alertas.log",
  
  # Horarios de trading (UTC)
  hora_inicio = 7,                  # 7:00 UTC
  hora_fin = 21,                    # 21:00 UTC
  dias_trading = c(1, 2, 3, 4, 5),  # Lunes a Viernes
  
  # Pares a monitorear
  pares_monitoreo = list(
    "EUR/USD" = "EURUSD=X",
    "GBP/USD" = "GBPUSD=X", 
    "USD/JPY" = "USDJPY=X"
  )
)

# =============================================================================
# FUNCIONES DE ALERTAS
# =============================================================================

# Verificar horario de trading
en_horario_trading <- function() {
  ahora <- Sys.time()
  hora_actual <- as.numeric(format(ahora, "%H"))
  dia_semana <- as.numeric(format(ahora, "%u"))
  
  hora_ok <- hora_actual >= ALERTAS_CONFIG$hora_inicio && 
             hora_actual <= ALERTAS_CONFIG$hora_fin
  dia_ok <- dia_semana %in% ALERTAS_CONFIG$dias_trading
  
  return(hora_ok && dia_ok)
}

# Reproducir alerta sonora
reproducir_alerta <- function(tipo = "oportunidad") {
  if (!alertas_disponibles$sonoras) return()
  
  tryCatch({
    if (tipo == "oportunidad") {
      beepr::beep(sound = 2)  # Sonido de moneda
    } else if (tipo == "error") {
      beepr::beep(sound = 9)  # Sonido de error
    } else if (tipo == "inicio") {
      beepr::beep(sound = 1)  # Sonido simple
    }
  }, error = function(e) {
    # Silencioso si falla
  })
}

# Mostrar alerta visual
mostrar_alerta_visual <- function(titulo, mensaje) {
  if (!alertas_disponibles$visuales) return()
  
  tryCatch({
    tcltk::tk_messageBox(
      type = "ok",
      message = mensaje,
      title = titulo,
      icon = "info"
    )
  }, error = function(e) {
    # Silencioso si falla
  })
}

# Escribir alerta en archivo
escribir_alerta <- function(par, analisis) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  
  mensaje <- paste0(
    "=== ALERTA SCALPING ===\n",
    "Fecha/Hora: ", timestamp, "\n",
    "Par: ", par, "\n",
    "Señal: ", analisis$señal, "\n",
    "Fuerza: ", analisis$fuerza, "\n",
    "Confirmaciones: ", analisis$confirmaciones, "\n",
    "Precio: ", sprintf("%.5f", analisis$precio), "\n",
    "RSI: ", sprintf("%.2f", analisis$rsi), "\n",
    "Razones: ", paste(analisis$razones, collapse = ", "), "\n",
    "========================\n\n"
  )
  
  tryCatch({
    cat(mensaje, file = ALERTAS_CONFIG$archivo_alertas, append = TRUE)
  }, error = function(e) {
    # Silencioso si falla
  })
}

# Escribir log de monitoreo
escribir_log <- function(mensaje) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  log_entry <- paste0("[", timestamp, "] ", mensaje, "\n")
  
  tryCatch({
    cat(log_entry, file = ALERTAS_CONFIG$archivo_log, append = TRUE)
  }, error = function(e) {
    # Silencioso si falla
  })
}

# =============================================================================
# ANÁLISIS RÁPIDO PARA ALERTAS
# =============================================================================

analisis_rapido_alertas <- function(simbolo) {
  tryCatch({
    # Obtener datos recientes
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = Sys.Date() - 20,
                       to = Sys.Date(),
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 15) {
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      Date = index(datos),
      Close = as.numeric(Cl(datos))
    )
    
    df <- na.omit(df)
    df <- df[order(df$Date), ]
    
    # Calcular indicadores básicos
    df$EMA8 <- EMA(df$Close, n = 8)
    df$EMA21 <- EMA(df$Close, n = 21)
    df$RSI <- RSI(df$Close, n = 14)
    
    # Análisis del último día
    ultimo <- tail(df, 1)
    anterior <- tail(df, 2)[1, ]
    
    if (any(is.na(c(ultimo$EMA8, ultimo$EMA21, ultimo$RSI)))) {
      return(NULL)
    }
    
    señal <- "NEUTRAL"
    fuerza <- 0
    confirmaciones <- 0
    razones <- c()
    
    # Análisis EMA
    if (ultimo$EMA8 > ultimo$EMA21) {
      if (anterior$EMA8 <= anterior$EMA21) {
        señal <- "COMPRA"
        fuerza <- fuerza + 3
        confirmaciones <- confirmaciones + 1
        razones <- c(razones, "Cruce EMA alcista")
      } else {
        fuerza <- fuerza + 1
        razones <- c(razones, "Tendencia alcista")
      }
    } else if (ultimo$EMA8 < ultimo$EMA21) {
      if (anterior$EMA8 >= anterior$EMA21) {
        señal <- "VENTA"
        fuerza <- fuerza + 3
        confirmaciones <- confirmaciones + 1
        razones <- c(razones, "Cruce EMA bajista")
      } else {
        fuerza <- fuerza + 1
        razones <- c(razones, "Tendencia bajista")
      }
    }
    
    # Análisis RSI
    if (ultimo$RSI < 30 && ultimo$RSI > anterior$RSI) {
      if (señal == "NEUTRAL") señal <- "COMPRA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "RSI sobreventa recuperándose")
    } else if (ultimo$RSI > 70 && ultimo$RSI < anterior$RSI) {
      if (señal == "NEUTRAL") señal <- "VENTA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "RSI sobrecompra corrigiéndose")
    }
    
    return(list(
      señal = señal,
      fuerza = fuerza,
      confirmaciones = confirmaciones,
      precio = ultimo$Close,
      rsi = ultimo$RSI,
      razones = razones
    ))
    
  }, error = function(e) {
    return(NULL)
  })
}

# =============================================================================
# MONITOREO AUTOMÁTICO CON ALERTAS
# =============================================================================

monitoreo_automatico_alertas <- function() {
  cat("🤖 MONITOREO AUTOMÁTICO CON ALERTAS\n")
  cat("===================================\n")
  cat("Intervalo:", ALERTAS_CONFIG$intervalo_minutos, "minutos\n")
  cat("Duración máxima:", ALERTAS_CONFIG$max_ciclos, "ciclos\n")
  cat("Horario:", ALERTAS_CONFIG$hora_inicio, ":00 -", ALERTAS_CONFIG$hora_fin, ":00 UTC\n\n")
  
  # Sonido de inicio
  reproducir_alerta("inicio")
  escribir_log("Monitoreo automático iniciado")
  
  ciclo <- 1
  alertas_enviadas <- 0
  
  while (ciclo <= ALERTAS_CONFIG$max_ciclos) {
    cat("🔄 CICLO", ciclo, "de", ALERTAS_CONFIG$max_ciclos, "\n")
    cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
    
    # Verificar horario de trading
    if (!en_horario_trading()) {
      cat("😴 Fuera de horario de trading. Esperando...\n")
      escribir_log("Fuera de horario de trading")
      
      # Esperar 1 hora si es fuera de horario
      Sys.sleep(60 * 60)
      ciclo <- ciclo + 1
      next
    }
    
    cat("✅ En horario de trading. Monitoreando...\n")
    
    oportunidades_detectadas <- 0
    
    for (par_nombre in names(ALERTAS_CONFIG$pares_monitoreo)) {
      simbolo <- ALERTAS_CONFIG$pares_monitoreo[[par_nombre]]
      
      cat("📊 Analizando", par_nombre, "...")
      
      analisis <- analisis_rapido_alertas(simbolo)
      
      if (!is.null(analisis)) {
        cat(" ✅\n")
        cat("   Señal:", analisis$señal, "| Fuerza:", analisis$fuerza, "\n")
        
        # Verificar si cumple criterios de alerta
        if (analisis$fuerza >= ALERTAS_CONFIG$fuerza_minima &&
            analisis$confirmaciones >= ALERTAS_CONFIG$confirmaciones_minimas &&
            analisis$señal %in% c("COMPRA", "VENTA")) {
          
          cat("🚨 ¡ALERTA ACTIVADA PARA", par_nombre, "!\n")
          
          # Preparar mensaje de alerta
          mensaje_alerta <- paste0(
            "🚨 OPORTUNIDAD SCALPING 🚨\n\n",
            "Par: ", par_nombre, "\n",
            "Señal: ", analisis$señal, "\n",
            "Fuerza: ", analisis$fuerza, "\n",
            "Precio: ", sprintf("%.5f", analisis$precio), "\n",
            "RSI: ", sprintf("%.2f", analisis$rsi), "\n",
            "Razones: ", paste(head(analisis$razones, 2), collapse = ", ")
          )
          
          # Enviar alertas
          reproducir_alerta("oportunidad")
          mostrar_alerta_visual("OPORTUNIDAD SCALPING", mensaje_alerta)
          escribir_alerta(par_nombre, analisis)
          
          alertas_enviadas <- alertas_enviadas + 1
          oportunidades_detectadas <- oportunidades_detectadas + 1
          escribir_log(paste("Alerta enviada para", par_nombre, "-", analisis$señal))
        }
      } else {
        cat(" ❌ Sin datos\n")
      }
      
      # Pausa entre pares
      Sys.sleep(5)
    }
    
    if (oportunidades_detectadas == 0) {
      cat("😴 No hay oportunidades en este ciclo\n")
      escribir_log("Sin oportunidades detectadas")
    }
    
    # Estadísticas del ciclo
    cat("📊 Ciclo", ciclo, "completado\n")
    cat("   Alertas enviadas:", alertas_enviadas, "\n")
    cat("   Oportunidades este ciclo:", oportunidades_detectadas, "\n")
    
    # Esperar hasta el próximo ciclo
    if (ciclo < ALERTAS_CONFIG$max_ciclos) {
      cat("⏳ Esperando", ALERTAS_CONFIG$intervalo_minutos, "minutos...\n\n")
      Sys.sleep(ALERTAS_CONFIG$intervalo_minutos * 60)
    }
    
    ciclo <- ciclo + 1
  }
  
  cat("🏁 MONITOREO COMPLETADO\n")
  cat("Total ciclos:", ciclo - 1, "\n")
  cat("Total alertas:", alertas_enviadas, "\n")
  escribir_log("Monitoreo automático finalizado")
  
  # Sonido de finalización
  reproducir_alerta("inicio")
}

# =============================================================================
# MONITOREO MANUAL CON ALERTAS
# =============================================================================

monitoreo_manual_alertas <- function() {
  cat("👤 MONITOREO MANUAL CON ALERTAS\n")
  cat("===============================\n")
  
  if (!en_horario_trading()) {
    cat("⚠️ Fuera de horario de trading óptimo\n")
    cat("¿Continuar de todos modos? (s/n): ")
    respuesta <- readline()
    if (tolower(respuesta) != "s") {
      return()
    }
  }
  
  oportunidades_encontradas <- 0
  
  for (par_nombre in names(ALERTAS_CONFIG$pares_monitoreo)) {
    simbolo <- ALERTAS_CONFIG$pares_monitoreo[[par_nombre]]
    
    cat("📊 Analizando", par_nombre, "...\n")
    
    analisis <- analisis_rapido_alertas(simbolo)
    
    if (!is.null(analisis)) {
      cat("   Señal:", analisis$señal, "\n")
      cat("   Fuerza:", analisis$fuerza, "\n")
      cat("   Precio:", sprintf("%.5f", analisis$precio), "\n")
      cat("   RSI:", sprintf("%.2f", analisis$rsi), "\n")
      
      if (analisis$fuerza >= ALERTAS_CONFIG$fuerza_minima &&
          analisis$señal %in% c("COMPRA", "VENTA")) {
        
        cat("⭐ OPORTUNIDAD DETECTADA!\n")
        
        mensaje_alerta <- paste0(
          "🎯 OPORTUNIDAD MANUAL\n\n",
          "Par: ", par_nombre, "\n",
          "Señal: ", analisis$señal, "\n",
          "Fuerza: ", analisis$fuerza
        )
        
        reproducir_alerta("oportunidad")
        mostrar_alerta_visual("OPORTUNIDAD MANUAL", mensaje_alerta)
        escribir_alerta(par_nombre, analisis)
        
        oportunidades_encontradas <- oportunidades_encontradas + 1
      }
    } else {
      cat("   ❌ Sin datos disponibles\n")
    }
    
    cat("\n")
  }
  
  if (oportunidades_encontradas == 0) {
    cat("😴 No hay oportunidades en este momento\n")
  } else {
    cat("🎉 Encontradas", oportunidades_encontradas, "oportunidades\n")
  }
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Monitoreo automático: monitoreo_automatico_alertas()\n")
cat("• Monitoreo manual: monitoreo_manual_alertas()\n")
cat("• Ver alertas: readLines('", ALERTAS_CONFIG$archivo_alertas, "')\n")
cat("• Ver logs: readLines('", ALERTAS_CONFIG$archivo_log, "')\n\n")

cat("🔧 CONFIGURACIÓN:\n")
cat("• Intervalo:", ALERTAS_CONFIG$intervalo_minutos, "minutos\n")
cat("• Fuerza mínima:", ALERTAS_CONFIG$fuerza_minima, "\n")
cat("• Horario trading:", ALERTAS_CONFIG$hora_inicio, ":00 -", ALERTAS_CONFIG$hora_fin, ":00 UTC\n\n")

cat("⚠️ IMPORTANTE:\n")
cat("• El monitoreo automático puede ejecutarse durante horas\n")
cat("• Verificar que las alertas sonoras no molesten\n")
cat("• Los archivos de log se crean automáticamente\n")
cat("• Usar durante horarios de mercado para mejores resultados\n\n")
