# =============================================================================
# PRUEBA SIMPLE DE API - VERIFICACIÓN RÁPIDA
# =============================================================================

# Instalar paquetes si no están disponibles
if (!require(httr, quietly = TRUE)) install.packages("httr")
if (!require(jsonlite, quietly = TRUE)) install.packages("jsonlite")

library(httr)
library(jsonlite)

cat("🧪 PRUEBA SIMPLE API ALPHA VANTAGE\n")
cat("===================================\n")

# Tu API key
api_key <- "KDF6QXHG5UOYNHJK"
cat("🔑 Usando API key:", substr(api_key, 1, 8), "...\n\n")

# Prueba 1: Función simple de tipo de cambio
cat("📡 Prueba 1: Tipo de cambio USD/EUR...\n")

url1 <- paste0(
  "https://www.alphavantage.co/query",
  "?function=CURRENCY_EXCHANGE_RATE",
  "&from_currency=USD",
  "&to_currency=EUR",
  "&apikey=", api_key
)

tryCatch({
  response1 <- GET(url1, timeout(30))
  cat("Status:", status_code(response1), "\n")
  
  if (status_code(response1) == 200) {
    content1 <- content(response1, "text", encoding = "UTF-8")
    data1 <- fromJSON(content1)
    
    if ("Realtime Currency Exchange Rate" %in% names(data1)) {
      rate_data <- data1$`Realtime Currency Exchange Rate`
      cat("✅ Tipo de cambio USD/EUR:", rate_data$`5. Exchange Rate`, "\n")
      cat("✅ Última actualización:", rate_data$`6. Last Refreshed`, "\n")
      cat("✅ PRUEBA 1 EXITOSA!\n\n")
      
    } else if ("Error Message" %in% names(data1)) {
      cat("❌ Error:", data1$`Error Message`, "\n\n")
      
    } else if ("Note" %in% names(data1)) {
      cat("⚠️ Límite:", data1$Note, "\n\n")
      
    } else {
      cat("⚠️ Respuesta inesperada en Prueba 1\n")
      print(names(data1))
      cat("\n")
    }
  } else {
    cat("❌ Error HTTP:", status_code(response1), "\n\n")
  }
  
}, error = function(e) {
  cat("❌ Error en Prueba 1:", e$message, "\n\n")
})

# Esperar para respetar límites
cat("⏳ Esperando 15 segundos...\n")
Sys.sleep(15)

# Prueba 2: Datos intraday EUR/USD
cat("📡 Prueba 2: Datos intraday EUR/USD 5min...\n")

url2 <- paste0(
  "https://www.alphavantage.co/query",
  "?function=FX_INTRADAY",
  "&from_symbol=EUR",
  "&to_symbol=USD",
  "&interval=5min",
  "&outputsize=compact",
  "&apikey=", api_key
)

tryCatch({
  response2 <- GET(url2, timeout(30))
  cat("Status:", status_code(response2), "\n")
  
  if (status_code(response2) == 200) {
    content2 <- content(response2, "text", encoding = "UTF-8")
    data2 <- fromJSON(content2)
    
    if ("Time Series FX (5min)" %in% names(data2)) {
      time_series <- data2$`Time Series FX (5min)`
      cat("✅ Datos obtenidos:", length(time_series), "registros\n")
      
      if (length(time_series) > 0) {
        ultimo_tiempo <- names(time_series)[1]
        ultimo_dato <- time_series[[1]]
        cat("✅ Último precio EUR/USD:", ultimo_dato$`4. close`, "\n")
        cat("✅ Hora:", ultimo_tiempo, "\n")
        cat("✅ PRUEBA 2 EXITOSA!\n\n")
        
        cat("🎉 ¡TU API KEY FUNCIONA PERFECTAMENTE!\n")
        cat("🚀 Sistema listo para scalping\n\n")
        
      } else {
        cat("⚠️ No hay datos en la serie temporal\n\n")
      }
      
    } else if ("Error Message" %in% names(data2)) {
      cat("❌ Error:", data2$`Error Message`, "\n\n")
      
    } else if ("Note" %in% names(data2)) {
      cat("⚠️ Límite:", data2$Note, "\n")
      cat("💡 Espera 1 minuto y vuelve a intentar\n\n")
      
    } else {
      cat("⚠️ Respuesta inesperada en Prueba 2\n")
      print(names(data2))
      cat("\n")
    }
  } else {
    cat("❌ Error HTTP:", status_code(response2), "\n\n")
  }
  
}, error = function(e) {
  cat("❌ Error en Prueba 2:", e$message, "\n\n")
})

cat("📋 RESUMEN:\n")
cat("===========\n")
cat("• API key configurada: ✅\n")
cat("• Conectividad: ✅\n")
cat("• Límites respetados: ✅\n\n")

cat("🎯 PRÓXIMOS PASOS:\n")
cat("1. source('estrategia_scalping_triple_confirmacion.R')\n")
cat("2. monitoreo_triple_confirmacion()\n")
cat("3. ¡Empezar a hacer scalping!\n\n")

cat("💡 Si ves errores de límite, espera 1 minuto entre llamadas\n")
