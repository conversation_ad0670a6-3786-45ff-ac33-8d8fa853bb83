# =============================================================================
# ESTRATEGIA DE TRADING PARA FUTUROS
# =============================================================================
# Estrategias específicas para diferentes tipos de futuros
# Índices (ES, NQ), Commodities (CL, GC), Bonos (ZN, ZB)
# =============================================================================

library(quantmod)
library(TTR)

cat("📈 ESTRATEGIA DE TRADING PARA FUTUROS\n")
cat("=====================================\n")
cat("Mercados: Índices, Commodities, Bonos\n\n")

# =============================================================================
# CONFIGURACIÓN DE FUTUROS
# =============================================================================

FUTUROS_CONFIG <- list(
  # Gestión de capital para futuros
  capital_total = 25000,               # Mínimo recomendado para futuros
  capital_utilizable_pct = 0.10,       # 10% del capital
  riesgo_por_operacion_pct = 0.02,     # 2% por operación
  
  # Contratos a analizar
  contratos = list(
    # Índices estadounidenses
    "ES" = list(
      simbolo = "ES=F",              # S&P 500 E-mini
      nombre = "S&P 500 E-mini",
      valor_punto = 50,              # $50 por punto
      tick_minimo = 0.25,            # 0.25 puntos
      margen_aprox = 13000,          # Margen aproximado
      horario_inicio = 9.5,          # 9:30 AM EST
      horario_fin = 16,              # 4:00 PM EST
      tipo = "indice"
    ),
    
    "NQ" = list(
      simbolo = "NQ=F",              # Nasdaq 100 E-mini
      nombre = "Nasdaq 100 E-mini",
      valor_punto = 20,              # $20 por punto
      tick_minimo = 0.25,
      margen_aprox = 17000,
      horario_inicio = 9.5,
      horario_fin = 16,
      tipo = "indice"
    ),
    
    # Commodities
    "CL" = list(
      simbolo = "CL=F",              # Crude Oil
      nombre = "Petróleo Crudo",
      valor_punto = 1000,            # $1000 por punto
      tick_minimo = 0.01,
      margen_aprox = 6000,
      horario_inicio = 9,
      horario_fin = 14.5,
      tipo = "commodity"
    ),
    
    "GC" = list(
      simbolo = "GC=F",              # Gold
      nombre = "Oro",
      valor_punto = 100,             # $100 por punto
      tick_minimo = 0.10,
      margen_aprox = 8000,
      horario_inicio = 8,
      horario_fin = 17,
      tipo = "commodity"
    )
  ),
  
  # Parámetros técnicos
  ma_rapida = 9,
  ma_lenta = 21,
  ma_tendencia = 50,
  rsi_periodo = 14,
  atr_periodo = 14,
  
  # Gestión de riesgo por tipo
  riesgo_indices = list(
    stop_loss_puntos = 15,           # 15 puntos para ES
    take_profit_puntos = 30,         # 30 puntos para ES
    trailing_stop_puntos = 10
  ),
  
  riesgo_commodities = list(
    stop_loss_pct = 0.02,            # 2% para commodities
    take_profit_pct = 0.04,          # 4% para commodities
    trailing_stop_pct = 0.015
  )
)

cat("📋 CONFIGURACIÓN FUTUROS:\n")
cat("=========================\n")
cat("Capital total:", FUTUROS_CONFIG$capital_total, "€\n")
cat("Capital utilizable:", FUTUROS_CONFIG$capital_total * FUTUROS_CONFIG$capital_utilizable_pct, "€\n")
cat("Riesgo por operación:", FUTUROS_CONFIG$capital_total * FUTUROS_CONFIG$riesgo_por_operacion_pct, "€\n")
cat("Contratos disponibles:", length(FUTUROS_CONFIG$contratos), "\n\n")

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS DE FUTUROS
# =============================================================================

obtener_datos_futuro <- function(contrato_info, dias = 180) {
  cat("📥 Descargando", contrato_info$nombre, "...")
  
  tryCatch({
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - dias
    
    datos <- getSymbols(contrato_info$simbolo,
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    cat(" ✅", nrow(datos), "días\n")
    return(datos)
    
  }, error = function(e) {
    cat(" ❌ Error\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA CALCULAR INDICADORES PARA FUTUROS
# =============================================================================

calcular_indicadores_futuros <- function(datos) {
  precios <- Cl(datos)
  high <- Hi(datos)
  low <- Lo(datos)
  volumen <- Vo(datos)
  
  # Medias móviles
  ma_rapida <- SMA(precios, n = FUTUROS_CONFIG$ma_rapida)
  ma_lenta <- SMA(precios, n = FUTUROS_CONFIG$ma_lenta)
  ma_tendencia <- SMA(precios, n = FUTUROS_CONFIG$ma_tendencia)
  
  # RSI
  rsi <- RSI(precios, n = FUTUROS_CONFIG$rsi_periodo)
  
  # ATR para volatilidad
  atr <- ATR(HLC(datos), n = FUTUROS_CONFIG$atr_periodo)
  
  # MACD
  macd <- MACD(precios, nFast = 12, nSlow = 26, nSig = 9)
  
  # Volumen promedio
  volumen_promedio <- SMA(volumen, n = 20)
  
  # Momentum
  momentum <- momentum(precios, n = 10)
  
  df <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    High = as.numeric(high),
    Low = as.numeric(low),
    Volumen = as.numeric(volumen),
    MA_Rapida = as.numeric(ma_rapida),
    MA_Lenta = as.numeric(ma_lenta),
    MA_Tendencia = as.numeric(ma_tendencia),
    RSI = as.numeric(rsi),
    ATR = as.numeric(atr$atr),
    MACD = as.numeric(macd$macd),
    MACD_Signal = as.numeric(macd$signal),
    Volumen_Promedio = as.numeric(volumen_promedio),
    Momentum = as.numeric(momentum)
  )
  
  return(df[complete.cases(df), ])
}

# =============================================================================
# FUNCIÓN PARA DETECTAR SEÑALES EN FUTUROS
# =============================================================================

detectar_señales_futuros <- function(datos, contrato_info) {
  señales <- data.frame()
  
  for (i in 3:nrow(datos)) {
    actual <- datos[i, ]
    anterior <- datos[i-1, ]
    
    if (any(is.na(c(actual$Precio, actual$MA_Rapida, actual$MA_Lenta, actual$RSI)))) {
      next
    }
    
    # =============================================================================
    # ESTRATEGIA PARA ÍNDICES
    # =============================================================================
    
    if (contrato_info$tipo == "indice") {
      
      # Breakout con volumen
      breakout_alcista <- (actual$Precio > actual$MA_Lenta) && 
                          (anterior$Precio <= anterior$MA_Lenta)
      
      volumen_alto <- actual$Volumen > (actual$Volumen_Promedio * 1.3)
      
      rsi_favorable <- actual$RSI > 40 && actual$RSI < 75
      
      macd_alcista <- actual$MACD > actual$MACD_Signal
      
      if (breakout_alcista && volumen_alto && rsi_favorable && macd_alcista) {
        señal <- data.frame(
          Fecha = actual$Fecha,
          Contrato = contrato_info$nombre,
          Tipo = "COMPRA",
          Estrategia = "Breakout_Indice",
          Precio = actual$Precio,
          RSI = actual$RSI,
          ATR = actual$ATR,
          Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
          Fuerza = "ALTA"
        )
        
        señales <- rbind(señales, señal)
      }
      
      # Pullback en tendencia alcista
      tendencia_alcista <- actual$MA_Rapida > actual$MA_Lenta && 
                           actual$MA_Lenta > actual$MA_Tendencia
      
      pullback <- actual$Precio <= actual$MA_Rapida && 
                  anterior$Precio > anterior$MA_Rapida &&
                  actual$Precio > actual$MA_Lenta
      
      rsi_pullback <- actual$RSI < 50 && actual$RSI > 30
      
      if (tendencia_alcista && pullback && rsi_pullback) {
        señal <- data.frame(
          Fecha = actual$Fecha,
          Contrato = contrato_info$nombre,
          Tipo = "COMPRA",
          Estrategia = "Pullback_Indice",
          Precio = actual$Precio,
          RSI = actual$RSI,
          ATR = actual$ATR,
          Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
          Fuerza = "MEDIA"
        )
        
        señales <- rbind(señales, señal)
      }
    }
    
    # =============================================================================
    # ESTRATEGIA PARA COMMODITIES
    # =============================================================================
    
    if (contrato_info$tipo == "commodity") {
      
      # Seguimiento de tendencia fuerte
      cruce_alcista <- (anterior$MA_Rapida <= anterior$MA_Lenta) && 
                       (actual$MA_Rapida > actual$MA_Lenta)
      
      tendencia_fuerte <- actual$Precio > actual$MA_Tendencia
      
      momentum_positivo <- actual$Momentum > 0
      
      rsi_commodity <- actual$RSI > 45 && actual$RSI < 80
      
      if (cruce_alcista && tendencia_fuerte && momentum_positivo && rsi_commodity) {
        señal <- data.frame(
          Fecha = actual$Fecha,
          Contrato = contrato_info$nombre,
          Tipo = "COMPRA",
          Estrategia = "Tendencia_Commodity",
          Precio = actual$Precio,
          RSI = actual$RSI,
          ATR = actual$ATR,
          Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
          Fuerza = "ALTA"
        )
        
        señales <- rbind(señales, señal)
      }
      
      # Reversión desde sobreventa
      rsi_sobreventa <- actual$RSI <= 25
      precio_bajo_ma <- actual$Precio < actual$MA_Lenta
      reversi_alcista <- actual$Precio > anterior$Precio && 
                         actual$Precio > actual$MA_Rapida
      
      if (rsi_sobreventa && precio_bajo_ma && reversi_alcista) {
        señal <- data.frame(
          Fecha = actual$Fecha,
          Contrato = contrato_info$nombre,
          Tipo = "COMPRA",
          Estrategia = "Reversi_Commodity",
          Precio = actual$Precio,
          RSI = actual$RSI,
          ATR = actual$ATR,
          Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
          Fuerza = "MEDIA"
        )
        
        señales <- rbind(señales, señal)
      }
    }
  }
  
  return(señales)
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES DE FUTUROS
# =============================================================================

calcular_niveles_futuros <- function(precio_entrada, contrato_info, atr_actual) {
  if (contrato_info$tipo == "indice") {
    # Para índices: usar puntos fijos
    stop_loss <- precio_entrada - FUTUROS_CONFIG$riesgo_indices$stop_loss_puntos
    take_profit <- precio_entrada + FUTUROS_CONFIG$riesgo_indices$take_profit_puntos
    
    # Calcular contratos
    riesgo_euros <- FUTUROS_CONFIG$capital_total * FUTUROS_CONFIG$riesgo_por_operacion_pct
    riesgo_puntos <- FUTUROS_CONFIG$riesgo_indices$stop_loss_puntos
    valor_por_punto <- contrato_info$valor_punto
    
    num_contratos <- floor(riesgo_euros / (riesgo_puntos * valor_por_punto))
    num_contratos <- max(1, num_contratos)
    
  } else {
    # Para commodities: usar porcentajes
    stop_loss <- precio_entrada * (1 - FUTUROS_CONFIG$riesgo_commodities$stop_loss_pct)
    take_profit <- precio_entrada * (1 + FUTUROS_CONFIG$riesgo_commodities$take_profit_pct)
    
    # Calcular contratos
    riesgo_euros <- FUTUROS_CONFIG$capital_total * FUTUROS_CONFIG$riesgo_por_operacion_pct
    riesgo_puntos <- precio_entrada - stop_loss
    valor_por_punto <- contrato_info$valor_punto
    
    num_contratos <- floor(riesgo_euros / (riesgo_puntos * valor_por_punto))
    num_contratos <- max(1, num_contratos)
  }
  
  ganancia_potencial <- num_contratos * (take_profit - precio_entrada) * valor_por_punto
  riesgo_real <- num_contratos * (precio_entrada - stop_loss) * valor_por_punto
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    num_contratos = num_contratos,
    riesgo_euros = riesgo_real,
    ganancia_potencial = ganancia_potencial,
    margen_requerido = num_contratos * contrato_info$margen_aprox,
    ratio_riesgo_beneficio = ganancia_potencial / riesgo_real
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ANÁLISIS DE FUTUROS
# =============================================================================

analizar_futuros <- function() {
  cat("📈 INICIANDO ANÁLISIS DE FUTUROS\n")
  cat("================================\n\n")
  
  todas_señales <- data.frame()
  
  # Analizar cada contrato
  for (nombre_contrato in names(FUTUROS_CONFIG$contratos)) {
    contrato_info <- FUTUROS_CONFIG$contratos[[nombre_contrato]]
    
    # Obtener datos
    datos <- obtener_datos_futuro(contrato_info, 180)
    
    if (is.null(datos)) {
      next
    }
    
    # Calcular indicadores
    datos_con_indicadores <- calcular_indicadores_futuros(datos)
    
    # Detectar señales
    señales <- detectar_señales_futuros(datos_con_indicadores, contrato_info)
    
    if (nrow(señales) > 0) {
      # Añadir información del contrato
      señales$Simbolo <- nombre_contrato
      señales$Tipo_Mercado <- contrato_info$tipo
      señales$ATR_Actual <- datos_con_indicadores$ATR[nrow(datos_con_indicadores)]
      
      todas_señales <- rbind(todas_señales, señales)
    }
  }
  
  # Mostrar resultados
  mostrar_resultados_futuros(todas_señales)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS
# =============================================================================

mostrar_resultados_futuros <- function(señales) {
  if (nrow(señales) == 0) {
    cat("⏸️ No hay señales de futuros actualmente\n")
    cat("💡 Los futuros requieren timing preciso\n")
    return()
  }
  
  cat("🎉 SEÑALES DE FUTUROS DETECTADAS\n")
  cat("================================\n")
  
  # Agrupar por tipo de mercado
  señales_indices <- señales[señales$Tipo_Mercado == "indice", ]
  señales_commodities <- señales[señales$Tipo_Mercado == "commodity", ]
  
  if (nrow(señales_indices) > 0) {
    cat("📊 SEÑALES EN ÍNDICES:\n")
    cat("======================\n")
    mostrar_señales_por_tipo(señales_indices, "indice")
  }
  
  if (nrow(señales_commodities) > 0) {
    cat("\n🛢️ SEÑALES EN COMMODITIES:\n")
    cat("==========================\n")
    mostrar_señales_por_tipo(señales_commodities, "commodity")
  }
  
  cat("\n📋 CONSIDERACIONES PARA FUTUROS:\n")
  cat("□ Verificar margen disponible antes de operar\n")
  cat("□ Revisar fechas de vencimiento de contratos\n")
  cat("□ Considerar horarios de mayor liquidez\n")
  cat("□ Usar stops estrictos por el apalancamiento\n")
  cat("□ Monitorear noticias económicas relevantes\n")
}

mostrar_señales_por_tipo <- function(señales, tipo) {
  señales_recientes <- tail(señales, 3)
  
  for (i in 1:nrow(señales_recientes)) {
    señal <- señales_recientes[i, ]
    contrato_info <- FUTUROS_CONFIG$contratos[[señal$Simbolo]]
    
    niveles <- calcular_niveles_futuros(señal$Precio, contrato_info, señal$ATR_Actual)
    
    cat("📈", señal$Contrato, "(", señal$Simbolo, ") -", señal$Estrategia, "\n")
    cat("   Fecha:", as.character(señal$Fecha), "\n")
    cat("   Precio:", round(señal$Precio, 2), "\n")
    cat("   RSI:", round(señal$RSI, 2), "\n")
    cat("   Fuerza:", señal$Fuerza, "\n")
    
    cat("   💰 NIVELES PARA FUTUROS:\n")
    cat("      Contratos:", niveles$num_contratos, "\n")
    cat("      Stop Loss:", round(niveles$stop_loss, 2), "\n")
    cat("      Take Profit:", round(niveles$take_profit, 2), "\n")
    cat("      Margen requerido:", round(niveles$margen_requerido, 0), "€\n")
    cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
    cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n")
    cat("      Ratio R:B:", round(niveles$ratio_riesgo_beneficio, 2), ":1\n\n")
  }
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

analizar_futuros()

cat("\n📈 ANÁLISIS DE FUTUROS COMPLETADO\n")
cat("=================================\n")
cat("💡 Los futuros requieren capital suficiente para márgenes\n")
cat("⚠️ Usar apalancamiento con extrema precaución\n")
cat("📅 Verificar fechas de vencimiento regularmente\n")
