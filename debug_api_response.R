# =============================================================================
# DEBUG API RESPONSE - SOLUCIÓN DEFINITIVA
# =============================================================================
# Script para debuggear y solucionar el problema de "Sin datos"
# =============================================================================

library(httr)
library(jsonlite)

cat("🔧 DEBUG API ALPHA VANTAGE\n")
cat("==========================\n")
cat("Solucionando problema 'Sin datos'\n\n")

api_key <- "KDF6QXHG5UOYNHJK"

# =============================================================================
# FUNCIÓN MEJORADA PARA OBTENER DATOS
# =============================================================================

obtener_datos_debug <- function(simbolo, timeframe = "1min") {
  cat("🔍 DEBUG:", simbolo, timeframe, "\n")
  
  # Construir URL
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_INTRADAY",
    "&from_symbol=", substr(simbolo, 1, 3),
    "&to_symbol=", substr(simbolo, 4, 6),
    "&interval=", timeframe,
    "&outputsize=compact",
    "&apikey=", api_key
  )
  
  cat("📡 URL:", substr(url, 1, 80), "...\n")
  
  tryCatch({
    # Realizar petición con configuración específica
    response <- GET(url, 
                   timeout(30),
                   add_headers("User-Agent" = "R-httr"))
    
    cat("📊 Status HTTP:", status_code(response), "\n")
    
    if (status_code(response) != 200) {
      cat("❌ Error HTTP:", status_code(response), "\n")
      return(NULL)
    }
    
    # Obtener contenido raw
    content_raw <- content(response, "text", encoding = "UTF-8")
    cat("📄 Contenido recibido (primeros 300 chars):\n")
    cat(substr(content_raw, 1, 300), "\n...\n\n")
    
    # Verificar si es JSON válido
    if (nchar(content_raw) < 10) {
      cat("❌ Respuesta muy corta\n")
      return(NULL)
    }
    
    # Parsear JSON
    data <- fromJSON(content_raw)
    cat("📋 Claves en respuesta:", paste(names(data), collapse = ", "), "\n")
    
    # Verificar errores específicos
    if ("Error Message" %in% names(data)) {
      cat("❌ Error de API:", data$`Error Message`, "\n")
      return(NULL)
    }
    
    if ("Note" %in% names(data)) {
      cat("⚠️ Límite de API:", data$Note, "\n")
      cat("💡 Esperando 60 segundos...\n")
      Sys.sleep(60)
      return(NULL)
    }
    
    # Buscar datos de series temporales
    time_series_key <- paste0("Time Series FX (", timeframe, ")")
    cat("🔍 Buscando clave:", time_series_key, "\n")
    
    if (time_series_key %in% names(data)) {
      time_series <- data[[time_series_key]]
      cat("✅ Series temporales encontradas:", length(time_series), "registros\n")
      
      if (length(time_series) == 0) {
        cat("⚠️ Series temporales vacías\n")
        return(NULL)
      }
      
      # Convertir a dataframe
      timestamps <- names(time_series)
      cat("📅 Rango de fechas:", timestamps[length(timestamps)], "a", timestamps[1], "\n")
      
      df <- data.frame(
        DateTime = as.POSIXct(timestamps),
        Open = as.numeric(sapply(time_series, function(x) x$`1. open`)),
        High = as.numeric(sapply(time_series, function(x) x$`2. high`)),
        Low = as.numeric(sapply(time_series, function(x) x$`3. low`)),
        Close = as.numeric(sapply(time_series, function(x) x$`4. close`)),
        stringsAsFactors = FALSE
      )
      
      # Ordenar por fecha más reciente primero
      df <- df[order(df$DateTime, decreasing = TRUE), ]
      
      cat("✅ DataFrame creado:", nrow(df), "filas\n")
      cat("💰 Último precio:", sprintf("%.5f", df$Close[1]), "\n")
      cat("⏰ Última actualización:", format(df$DateTime[1], "%Y-%m-%d %H:%M:%S"), "\n")
      
      return(df)
      
    } else {
      cat("❌ No se encontró clave de series temporales\n")
      cat("📋 Claves disponibles:", paste(names(data), collapse = ", "), "\n")
      
      # Mostrar estructura completa para debug
      cat("📊 Estructura completa de la respuesta:\n")
      str(data)
      
      return(NULL)
    }
    
  }, error = function(e) {
    cat("❌ Error en petición:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# PROBAR CON DIFERENTES CONFIGURACIONES
# =============================================================================

cat("🧪 PROBANDO DIFERENTES CONFIGURACIONES...\n")
cat("==========================================\n")

# Prueba 1: EUR/USD con 5min (más estable)
cat("\n1️⃣ PRUEBA EUR/USD 5min:\n")
datos_eur_5min <- obtener_datos_debug("EURUSD", "5min")

if (!is.null(datos_eur_5min)) {
  cat("🎉 ¡ÉXITO CON 5MIN!\n")
} else {
  cat("⏳ Esperando 15 segundos...\n")
  Sys.sleep(15)
  
  # Prueba 2: EUR/USD con 15min
  cat("\n2️⃣ PRUEBA EUR/USD 15min:\n")
  datos_eur_15min <- obtener_datos_debug("EURUSD", "15min")
  
  if (!is.null(datos_eur_15min)) {
    cat("🎉 ¡ÉXITO CON 15MIN!\n")
  } else {
    cat("⏳ Esperando 15 segundos...\n")
    Sys.sleep(15)
    
    # Prueba 3: Función diferente (tipo de cambio)
    cat("\n3️⃣ PRUEBA TIPO DE CAMBIO:\n")
    
    url_exchange <- paste0(
      "https://www.alphavantage.co/query",
      "?function=CURRENCY_EXCHANGE_RATE",
      "&from_currency=EUR",
      "&to_currency=USD",
      "&apikey=", api_key
    )
    
    tryCatch({
      response_ex <- GET(url_exchange, timeout(30))
      content_ex <- content(response_ex, "text", encoding = "UTF-8")
      data_ex <- fromJSON(content_ex)
      
      if ("Realtime Currency Exchange Rate" %in% names(data_ex)) {
        rate_data <- data_ex$`Realtime Currency Exchange Rate`
        cat("✅ Tipo de cambio EUR/USD:", rate_data$`5. Exchange Rate`, "\n")
        cat("✅ API FUNCIONANDO - Problema con datos intraday\n")
      }
    }, error = function(e) {
      cat("❌ Error también en tipo de cambio\n")
    })
  }
}

# =============================================================================
# SOLUCIÓN ALTERNATIVA: USAR TIMEFRAME MÁS LARGO
# =============================================================================

cat("\n", paste(rep("=", 50), collapse = ""), "\n")
cat("💡 SOLUCIÓN ALTERNATIVA\n")
cat(paste(rep("=", 50), collapse = ""), "\n")

if (exists("datos_eur_5min") && !is.null(datos_eur_5min)) {
  cat("✅ Datos de 5min funcionan - Adaptando estrategia\n")
  timeframe_funcional <- "5min"
} else if (exists("datos_eur_15min") && !is.null(datos_eur_15min)) {
  cat("✅ Datos de 15min funcionan - Adaptando estrategia\n")
  timeframe_funcional <- "15min"
} else {
  cat("⚠️ Problemas con datos intraday\n")
  cat("💡 Posibles causas:\n")
  cat("   • API key nueva (esperar 30-60 minutos)\n")
  cat("   • Límites de API alcanzados\n")
  cat("   • Problemas temporales de Alpha Vantage\n")
  cat("   • Horario fuera de mercado\n\n")
  
  cat("🔧 SOLUCIONES:\n")
  cat("1. Esperar 30-60 minutos y volver a intentar\n")
  cat("2. Verificar email de confirmación de Alpha Vantage\n")
  cat("3. Probar durante horarios de mercado (8:00-22:00 UTC)\n")
  cat("4. Contactar soporte Alpha Vantage si persiste\n\n")
  
  timeframe_funcional <- NULL
}

# =============================================================================
# ACTUALIZAR CONFIGURACIÓN SI HAY TIMEFRAME FUNCIONAL
# =============================================================================

if (!is.null(timeframe_funcional)) {
  cat("🔧 ACTUALIZANDO CONFIGURACIÓN...\n")
  
  # Actualizar estrategia con timeframe funcional
  if (file.exists("estrategia_scalping_triple_confirmacion.R")) {
    tryCatch({
      lineas <- readLines("estrategia_scalping_triple_confirmacion.R")
      
      for (i in 1:length(lineas)) {
        if (grepl('timeframe = "1min"', lineas[i])) {
          lineas[i] <- paste0('  timeframe = "', timeframe_funcional, '",       # Timeframe funcional')
          break
        }
      }
      
      writeLines(lineas, "estrategia_scalping_triple_confirmacion.R")
      cat("✅ Estrategia actualizada para usar", timeframe_funcional, "\n")
      
    }, error = function(e) {
      cat("⚠️ No se pudo actualizar automáticamente\n")
    })
  }
  
  cat("\n🚀 PROBANDO ESTRATEGIA ACTUALIZADA...\n")
  source("estrategia_scalping_triple_confirmacion.R")
  
  cat("🎯 Ejecutando monitoreo con", timeframe_funcional, "...\n")
  # No ejecutar automáticamente para evitar spam de API
  cat("💡 Ejecuta manualmente: monitoreo_triple_confirmacion()\n")
}

cat("\n📋 RESUMEN FINAL:\n")
cat("==================\n")
cat("• API key: ✅ Configurada\n")
cat("• Conectividad: ✅ OK\n")
if (!is.null(timeframe_funcional)) {
  cat("• Timeframe funcional:", timeframe_funcional, "✅\n")
  cat("• Estado: 🎉 LISTO PARA SCALPING\n")
} else {
  cat("• Datos intraday: ❌ Problema temporal\n")
  cat("• Estado: ⏳ Esperar y reintentar\n")
}

cat("\n🎯 PRÓXIMOS PASOS:\n")
if (!is.null(timeframe_funcional)) {
  cat("1. monitoreo_triple_confirmacion()\n")
  cat("2. source('sistema_alertas_scalping.R')\n")
  cat("3. ¡Empezar scalping!\n")
} else {
  cat("1. Esperar 30-60 minutos\n")
  cat("2. Verificar email de Alpha Vantage\n")
  cat("3. Volver a ejecutar este script\n")
}
