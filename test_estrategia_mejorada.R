# =============================================================================
# TEST ESTRATEGIA MEJORADA - SOLUCIÓN PARA API ISSUES
# =============================================================================
# Versión mejorada que maneja mejor los problemas de API
# =============================================================================

library(httr)
library(jsonlite)
library(quantmod)
library(TTR)

cat("🧪 TEST ESTRATEGIA MEJORADA\n")
cat("===========================\n")
cat("Probando con diferentes timeframes y configuraciones\n\n")

api_key <- "KDF6QXHG5UOYNHJK"

# =============================================================================
# FUNCIÓN ROBUSTA PARA OBTENER DATOS
# =============================================================================

obtener_datos_robusto <- function(simbolo, timeframes = c("5min", "15min", "30min")) {
  cat("🔄 Probando", simbolo, "con múltiples timeframes...\n")
  
  for (tf in timeframes) {
    cat("  📡 Intentando", tf, "...")
    
    url <- paste0(
      "https://www.alphavantage.co/query",
      "?function=FX_INTRADAY",
      "&from_symbol=", substr(simbolo, 1, 3),
      "&to_symbol=", substr(simbolo, 4, 6),
      "&interval=", tf,
      "&outputsize=compact",
      "&apikey=", api_key
    )
    
    tryCatch({
      response <- GET(url, timeout(30))
      
      if (status_code(response) == 200) {
        content_raw <- content(response, "text", encoding = "UTF-8")
        data <- fromJSON(content_raw)
        
        # Verificar si hay datos válidos
        time_series_key <- paste0("Time Series FX (", tf, ")")
        
        if (time_series_key %in% names(data)) {
          time_series <- data[[time_series_key]]
          
          if (length(time_series) > 0) {
            cat(" ✅ Éxito!\n")
            
            # Convertir a dataframe
            df <- data.frame(
              DateTime = as.POSIXct(names(time_series)),
              Open = as.numeric(sapply(time_series, function(x) x$`1. open`)),
              High = as.numeric(sapply(time_series, function(x) x$`2. high`)),
              Low = as.numeric(sapply(time_series, function(x) x$`3. low`)),
              Close = as.numeric(sapply(time_series, function(x) x$`4. close`)),
              stringsAsFactors = FALSE
            )
            
            # Ordenar por fecha más reciente primero
            df <- df[order(df$DateTime, decreasing = TRUE), ]
            
            cat("    📊 Registros:", nrow(df), "\n")
            cat("    💰 Último precio:", sprintf("%.5f", df$Close[1]), "\n")
            cat("    ⏰ Última actualización:", format(df$DateTime[1], "%H:%M:%S"), "\n")
            
            return(list(datos = df, timeframe = tf))
          }
        } else if ("Error Message" %in% names(data)) {
          cat(" ❌ Error:", data$`Error Message`, "\n")
        } else if ("Note" %in% names(data)) {
          cat(" ⚠️ Límite API\n")
          Sys.sleep(15)
        } else {
          cat(" ⚠️ Formato inesperado\n")
        }
      } else {
        cat(" ❌ HTTP", status_code(response), "\n")
      }
      
    }, error = function(e) {
      cat(" ❌ Error:", e$message, "\n")
    })
    
    # Pausa entre intentos
    if (tf != timeframes[length(timeframes)]) {
      Sys.sleep(12)  # Respetar límites de API
    }
  }
  
  cat("  ❌ No se pudieron obtener datos con ningún timeframe\n")
  return(NULL)
}

# =============================================================================
# ANÁLISIS SIMPLIFICADO
# =============================================================================

analizar_simple <- function(datos, timeframe) {
  if (nrow(datos) < 30) {
    return(list(señal = "DATOS_INSUFICIENTES", fuerza = 0))
  }
  
  # Ordenar cronológicamente para cálculos
  datos_calc <- datos[order(datos$DateTime), ]
  
  # Calcular indicadores básicos
  datos_calc$EMA8 <- EMA(datos_calc$Close, n = 8)
  datos_calc$EMA21 <- EMA(datos_calc$Close, n = 21)
  datos_calc$RSI <- RSI(datos_calc$Close, n = 14)
  
  # Volver a ordenar por fecha más reciente
  datos_calc <- datos_calc[order(datos_calc$DateTime, decreasing = TRUE), ]
  
  # Análisis del último registro
  ultimo <- datos_calc[1, ]
  anterior <- datos_calc[2, ]
  
  if (any(is.na(c(ultimo$EMA8, ultimo$EMA21, ultimo$RSI)))) {
    return(list(señal = "INDICADORES_INVALIDOS", fuerza = 0))
  }
  
  señal <- "NEUTRAL"
  fuerza <- 0
  razones <- c()
  
  # Señales de compra
  if (ultimo$EMA8 > ultimo$EMA21) {
    if (anterior$EMA8 <= anterior$EMA21) {
      señal <- "COMPRA"
      fuerza <- fuerza + 3
      razones <- c(razones, "Cruce EMA alcista")
    } else {
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA alcista")
    }
  }
  
  if (ultimo$RSI < 30 && ultimo$RSI > anterior$RSI) {
    if (señal == "NEUTRAL") señal <- "COMPRA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobreventa recuperándose")
  } else if (ultimo$RSI >= 40 && ultimo$RSI <= 60 && ultimo$RSI > anterior$RSI) {
    fuerza <- fuerza + 1
    razones <- c(razones, "RSI momentum alcista")
  }
  
  # Señales de venta
  if (ultimo$EMA8 < ultimo$EMA21) {
    if (anterior$EMA8 >= anterior$EMA21) {
      señal <- "VENTA"
      fuerza <- fuerza + 3
      razones <- c(razones, "Cruce EMA bajista")
    } else {
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA bajista")
    }
  }
  
  if (ultimo$RSI > 70 && ultimo$RSI < anterior$RSI) {
    if (señal == "NEUTRAL") señal <- "VENTA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobrecompra corrigiéndose")
  }
  
  return(list(
    señal = señal,
    fuerza = fuerza,
    precio = ultimo$Close,
    rsi = ultimo$RSI,
    ema8 = ultimo$EMA8,
    ema21 = ultimo$EMA21,
    timeframe = timeframe,
    razones = razones
  ))
}

# =============================================================================
# TEST PRINCIPAL
# =============================================================================

cat("🎯 INICIANDO TEST PRINCIPAL\n")
cat("===========================\n")

pares_test <- c("EURUSD", "GBPUSD")
resultados <- list()

for (par in pares_test) {
  cat("\n📊 ANALIZANDO", par, "\n")
  cat(paste(rep("-", 30), collapse = ""), "\n")
  
  # Obtener datos con función robusta
  resultado_datos <- obtener_datos_robusto(par)
  
  if (!is.null(resultado_datos)) {
    # Analizar datos
    analisis <- analizar_simple(resultado_datos$datos, resultado_datos$timeframe)
    
    cat("🔍 Señal:", analisis$señal, "\n")
    cat("💪 Fuerza:", analisis$fuerza, "\n")
    cat("💰 Precio:", sprintf("%.5f", analisis$precio), "\n")
    cat("📊 RSI:", sprintf("%.2f", analisis$rsi), "\n")
    cat("📈 EMA8:", sprintf("%.5f", analisis$ema8), "\n")
    cat("📈 EMA21:", sprintf("%.5f", analisis$ema21), "\n")
    cat("⏰ Timeframe:", analisis$timeframe, "\n")
    
    if (length(analisis$razones) > 0) {
      cat("📋 Razones:\n")
      for (razon in analisis$razones) {
        cat("   •", razon, "\n")
      }
    }
    
    if (analisis$fuerza >= 2) {
      cat("⭐ SEÑAL INTERESANTE!\n")
      resultados[[par]] <- analisis
    }
    
  } else {
    cat("❌ No se pudieron obtener datos para", par, "\n")
  }
  
  cat("\n")
}

# =============================================================================
# RESUMEN FINAL
# =============================================================================

cat(paste(rep("=", 50), collapse = ""), "\n")
cat("📊 RESUMEN DEL TEST\n")
cat(paste(rep("=", 50), collapse = ""), "\n")

if (length(resultados) > 0) {
  cat("🎉 ¡TEST EXITOSO!\n")
  cat("✅ Pares con señales:", length(resultados), "\n\n")
  
  for (par in names(resultados)) {
    res <- resultados[[par]]
    cat("🎯", par, "- Señal:", res$señal, "- Fuerza:", res$fuerza, "\n")
    cat("   Timeframe:", res$timeframe, "- Precio:", sprintf("%.5f", res$precio), "\n")
  }
  
  cat("\n🚀 SISTEMA FUNCIONANDO!\n")
  cat("💡 Ahora puedes usar:\n")
  cat("   • source('estrategia_scalping_triple_confirmacion.R')\n")
  cat("   • monitoreo_triple_confirmacion()\n")
  
} else {
  cat("⚠️ No se detectaron señales en este momento\n")
  cat("💡 Posibles causas:\n")
  cat("   • Mercado en rango lateral\n")
  cat("   • Horario de baja volatilidad\n")
  cat("   • Configuración de indicadores muy estricta\n")
  
  cat("\n🔧 Recomendaciones:\n")
  cat("   • Probar en horarios de mayor volatilidad\n")
  cat("   • Ajustar parámetros de indicadores\n")
  cat("   • Esperar movimientos de mercado\n")
}

cat("\n⏰ Test completado:", format(Sys.time(), "%H:%M:%S"), "\n")
cat("🔄 Para monitoreo continuo, ejecutar cada 15-30 minutos\n")
