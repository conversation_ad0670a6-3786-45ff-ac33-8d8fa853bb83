# =============================================================================
# ESTRATEGIA SCALPING: TRIPLE CONFIRMACIÓN
# =============================================================================
# Estrategia profesional para scalping con 3 confirmaciones:
# 1. EMA Cruzadas (8/21) - Dirección de tendencia
# 2. RSI (14) - Momentum y puntos de entrada
# 3. Bandas de Bollinger - Volatilidad y niveles
# =============================================================================

library(httr)
library(jsonlite)
library(quantmod)
library(TTR)
library(dplyr)

cat("🎯 ESTRATEGIA SCALPING: TRIPLE CONFIRMACIÓN\n")
cat("==========================================\n")
cat("EMA 8/21 + RSI 14 + Bandas de Bollinger\n")
cat("Timeframe principal: 1 minuto\n\n")

# =============================================================================
# CONFIGURACIÓN DE LA ESTRATEGIA
# =============================================================================

ESTRATEGIA_CONFIG <- list(
  # Configuración Alpha Vantage
  api_key = "DEMO",  # 🔑 CAMBIAR POR TU API KEY
  
  # Gestión de capital para scalping agresivo
  capital_total = 20000,
  riesgo_por_operacion_pct = 0.5,    # 0.5% por operación (conservador)
  reward_risk_ratio = 2.0,           # 1:2 (risk:reward)
  max_operaciones_simultaneas = 2,
  max_operaciones_dia = 8,
  
  # Pares optimizados para scalping
  pares_scalping = list(
    "EUR/USD" = "EURUSD",  # Spread bajo, alta liquidez
    "GBP/USD" = "GBPUSD",  # Volatilidad media-alta
    "USD/JPY" = "USDJPY",  # Movimientos consistentes
    "GBP/JPY" = "GBPJPY"   # Alta volatilidad (avanzado)
  ),
  
  # Indicadores técnicos
  ema_rapida = 8,           # EMA rápida
  ema_lenta = 21,           # EMA lenta
  rsi_periodo = 14,         # RSI estándar
  rsi_sobrecompra = 70,     # Nivel de sobrecompra
  rsi_sobreventa = 30,      # Nivel de sobreventa
  rsi_zona_neutral_min = 40, # Zona neutral mínima
  rsi_zona_neutral_max = 60, # Zona neutral máxima
  
  # Bandas de Bollinger
  bb_periodo = 20,          # Período para BB
  bb_desviaciones = 2,      # Desviaciones estándar
  
  # Configuración de señales
  min_confirmaciones = 2,   # Mínimo 2 de 3 confirmaciones
  timeframe = "1min",       # Timeframe principal
  
  # Stop Loss y Take Profit (en pips)
  stop_loss_pips = 8,       # 8 pips SL (ajustable según par)
  take_profit_pips = 16     # 16 pips TP (1:2 ratio)
)

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS EN TIEMPO REAL
# =============================================================================

obtener_datos_scalping <- function(simbolo, outputsize = "compact") {
  cat("📡 Obteniendo datos", ESTRATEGIA_CONFIG$timeframe, "de", simbolo, "...")
  
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_INTRADAY",
    "&from_symbol=", substr(simbolo, 1, 3),
    "&to_symbol=", substr(simbolo, 4, 6),
    "&interval=", ESTRATEGIA_CONFIG$timeframe,
    "&outputsize=", outputsize,
    "&apikey=", ESTRATEGIA_CONFIG$api_key
  )
  
  tryCatch({
    response <- GET(url, timeout(30))
    
    if (status_code(response) != 200) {
      cat(" ❌ Error HTTP\n")
      return(NULL)
    }
    
    data <- fromJSON(content(response, "text"))
    
    if ("Error Message" %in% names(data)) {
      cat(" ❌ Error API\n")
      return(NULL)
    }
    
    if ("Note" %in% names(data)) {
      cat(" ⚠️ Límite API\n")
      return(NULL)
    }
    
    time_series_key <- paste0("Time Series FX (", ESTRATEGIA_CONFIG$timeframe, ")")
    
    if (!time_series_key %in% names(data)) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    time_series <- data[[time_series_key]]
    
    if (length(time_series) == 0) {
      cat(" ❌ Datos vacíos\n")
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      DateTime = as.POSIXct(names(time_series)),
      Open = as.numeric(sapply(time_series, function(x) x$`1. open`)),
      High = as.numeric(sapply(time_series, function(x) x$`2. high`)),
      Low = as.numeric(sapply(time_series, function(x) x$`3. low`)),
      Close = as.numeric(sapply(time_series, function(x) x$`4. close`)),
      stringsAsFactors = FALSE
    )
    
    # Ordenar cronológicamente para cálculos
    df <- df[order(df$DateTime), ]
    
    cat(" ✅", nrow(df), "velas\n")
    return(df)
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA CALCULAR INDICADORES
# =============================================================================

calcular_indicadores_triple <- function(datos) {
  if (is.null(datos) || nrow(datos) < 50) {
    return(NULL)
  }
  
  # Calcular EMAs
  datos$EMA_Rapida <- EMA(datos$Close, n = ESTRATEGIA_CONFIG$ema_rapida)
  datos$EMA_Lenta <- EMA(datos$Close, n = ESTRATEGIA_CONFIG$ema_lenta)
  
  # Calcular RSI
  datos$RSI <- RSI(datos$Close, n = ESTRATEGIA_CONFIG$rsi_periodo)
  
  # Calcular Bandas de Bollinger
  bb <- BBands(datos$Close, 
               n = ESTRATEGIA_CONFIG$bb_periodo, 
               sd = ESTRATEGIA_CONFIG$bb_desviaciones)
  datos$BB_Upper <- bb[, "up"]
  datos$BB_Middle <- bb[, "mavg"]
  datos$BB_Lower <- bb[, "dn"]
  
  # Calcular ancho de bandas (volatilidad)
  datos$BB_Width <- (datos$BB_Upper - datos$BB_Lower) / datos$BB_Middle * 100
  
  # Posición del precio en las bandas
  datos$BB_Position <- (datos$Close - datos$BB_Lower) / (datos$BB_Upper - datos$BB_Lower)
  
  # Momentum adicional
  datos$Momentum <- c(NA, diff(datos$Close))
  datos$Volatilidad <- runSD(datos$Close, n = 10)
  
  # Señales individuales
  datos$EMA_Signal <- ifelse(datos$EMA_Rapida > datos$EMA_Lenta, 1, 
                            ifelse(datos$EMA_Rapida < datos$EMA_Lenta, -1, 0))
  
  # Volver a ordenar por fecha más reciente primero
  datos <- datos[order(datos$DateTime, decreasing = TRUE), ]
  
  return(datos)
}

# =============================================================================
# FUNCIÓN DE ANÁLISIS TRIPLE CONFIRMACIÓN
# =============================================================================

analizar_triple_confirmacion <- function(datos_con_indicadores) {
  if (is.null(datos_con_indicadores) || nrow(datos_con_indicadores) < 10) {
    return(list(
      señal = "SIN_DATOS",
      fuerza = 0,
      confirmaciones = 0,
      detalles = list()
    ))
  }
  
  # Datos actuales y anteriores
  actual <- datos_con_indicadores[1, ]
  anterior <- datos_con_indicadores[2, ]
  
  # Verificar datos válidos
  if (any(is.na(c(actual$EMA_Rapida, actual$EMA_Lenta, actual$RSI, 
                  actual$BB_Upper, actual$BB_Lower)))) {
    return(list(
      señal = "DATOS_INVALIDOS",
      fuerza = 0,
      confirmaciones = 0,
      detalles = list()
    ))
  }
  
  confirmaciones_compra <- 0
  confirmaciones_venta <- 0
  detalles_analisis <- list()
  
  # =============================================================================
  # CONFIRMACIÓN 1: EMA CRUZADAS
  # =============================================================================
  
  ema_señal <- "NEUTRAL"
  
  # Cruce alcista
  if (actual$EMA_Rapida > actual$EMA_Lenta && 
      anterior$EMA_Rapida <= anterior$EMA_Lenta) {
    ema_señal <- "COMPRA_FUERTE"
    confirmaciones_compra <- confirmaciones_compra + 2
  }
  # Tendencia alcista establecida
  else if (actual$EMA_Rapida > actual$EMA_Lenta && 
           actual$EMA_Rapida > anterior$EMA_Rapida) {
    ema_señal <- "COMPRA"
    confirmaciones_compra <- confirmaciones_compra + 1
  }
  # Cruce bajista
  else if (actual$EMA_Rapida < actual$EMA_Lenta && 
           anterior$EMA_Rapida >= anterior$EMA_Lenta) {
    ema_señal <- "VENTA_FUERTE"
    confirmaciones_venta <- confirmaciones_venta + 2
  }
  # Tendencia bajista establecida
  else if (actual$EMA_Rapida < actual$EMA_Lenta && 
           actual$EMA_Rapida < anterior$EMA_Rapida) {
    ema_señal <- "VENTA"
    confirmaciones_venta <- confirmaciones_venta + 1
  }
  
  detalles_analisis$ema <- list(
    señal = ema_señal,
    ema_rapida = actual$EMA_Rapida,
    ema_lenta = actual$EMA_Lenta,
    diferencia = actual$EMA_Rapida - actual$EMA_Lenta
  )
  
  # =============================================================================
  # CONFIRMACIÓN 2: RSI MOMENTUM
  # =============================================================================
  
  rsi_señal <- "NEUTRAL"
  
  # RSI sobreventa recuperándose
  if (actual$RSI < ESTRATEGIA_CONFIG$rsi_sobreventa && 
      actual$RSI > anterior$RSI) {
    rsi_señal <- "COMPRA_FUERTE"
    confirmaciones_compra <- confirmaciones_compra + 2
  }
  # RSI en zona neutral con momentum alcista
  else if (actual$RSI >= ESTRATEGIA_CONFIG$rsi_zona_neutral_min && 
           actual$RSI <= ESTRATEGIA_CONFIG$rsi_zona_neutral_max && 
           actual$RSI > anterior$RSI) {
    rsi_señal <- "COMPRA"
    confirmaciones_compra <- confirmaciones_compra + 1
  }
  # RSI sobrecompra corrigiéndose
  else if (actual$RSI > ESTRATEGIA_CONFIG$rsi_sobrecompra && 
           actual$RSI < anterior$RSI) {
    rsi_señal <- "VENTA_FUERTE"
    confirmaciones_venta <- confirmaciones_venta + 2
  }
  # RSI en zona neutral con momentum bajista
  else if (actual$RSI >= ESTRATEGIA_CONFIG$rsi_zona_neutral_min && 
           actual$RSI <= ESTRATEGIA_CONFIG$rsi_zona_neutral_max && 
           actual$RSI < anterior$RSI) {
    rsi_señal <- "VENTA"
    confirmaciones_venta <- confirmaciones_venta + 1
  }
  
  detalles_analisis$rsi <- list(
    señal = rsi_señal,
    valor = actual$RSI,
    cambio = actual$RSI - anterior$RSI
  )
  
  # =============================================================================
  # CONFIRMACIÓN 3: BANDAS DE BOLLINGER
  # =============================================================================
  
  bb_señal <- "NEUTRAL"
  
  # Rebote en banda inferior
  if (actual$Close <= actual$BB_Lower && actual$Momentum > 0) {
    bb_señal <- "COMPRA_FUERTE"
    confirmaciones_compra <- confirmaciones_compra + 2
  }
  # Precio cerca de banda inferior con momentum alcista
  else if (actual$BB_Position < 0.2 && actual$Momentum > 0) {
    bb_señal <- "COMPRA"
    confirmaciones_compra <- confirmaciones_compra + 1
  }
  # Rechazo en banda superior
  else if (actual$Close >= actual$BB_Upper && actual$Momentum < 0) {
    bb_señal <- "VENTA_FUERTE"
    confirmaciones_venta <- confirmaciones_venta + 2
  }
  # Precio cerca de banda superior con momentum bajista
  else if (actual$BB_Position > 0.8 && actual$Momentum < 0) {
    bb_señal <- "VENTA"
    confirmaciones_venta <- confirmaciones_venta + 1
  }
  
  detalles_analisis$bollinger <- list(
    señal = bb_señal,
    posicion = actual$BB_Position,
    ancho = actual$BB_Width,
    precio = actual$Close,
    banda_superior = actual$BB_Upper,
    banda_inferior = actual$BB_Lower
  )
  
  # =============================================================================
  # DECISIÓN FINAL
  # =============================================================================
  
  señal_final <- "NEUTRAL"
  fuerza_final <- 0
  confirmaciones_totales <- 0
  
  if (confirmaciones_compra >= ESTRATEGIA_CONFIG$min_confirmaciones) {
    señal_final <- "COMPRA"
    fuerza_final <- confirmaciones_compra
    confirmaciones_totales <- confirmaciones_compra
  } else if (confirmaciones_venta >= ESTRATEGIA_CONFIG$min_confirmaciones) {
    señal_final <- "VENTA"
    fuerza_final <- confirmaciones_venta
    confirmaciones_totales <- confirmaciones_venta
  }
  
  return(list(
    señal = señal_final,
    fuerza = fuerza_final,
    confirmaciones = confirmaciones_totales,
    precio = actual$Close,
    datetime = actual$DateTime,
    detalles = detalles_analisis,
    volatilidad = actual$BB_Width
  ))
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES DE TRADING
# =============================================================================

calcular_niveles_scalping <- function(precio_entrada, señal, simbolo) {
  # Determinar valor del pip según el par
  pip_value <- if (grepl("JPY", simbolo)) 0.01 else 0.0001
  
  if (señal == "COMPRA") {
    stop_loss <- precio_entrada - (ESTRATEGIA_CONFIG$stop_loss_pips * pip_value)
    take_profit <- precio_entrada + (ESTRATEGIA_CONFIG$take_profit_pips * pip_value)
  } else if (señal == "VENTA") {
    stop_loss <- precio_entrada + (ESTRATEGIA_CONFIG$stop_loss_pips * pip_value)
    take_profit <- precio_entrada - (ESTRATEGIA_CONFIG$take_profit_pips * pip_value)
  } else {
    return(NULL)
  }
  
  # Calcular tamaño de posición
  riesgo_euros <- ESTRATEGIA_CONFIG$capital_total * (ESTRATEGIA_CONFIG$riesgo_por_operacion_pct / 100)
  riesgo_pips <- abs(precio_entrada - stop_loss) / pip_value
  
  # Tamaño de lote (simplificado)
  tamaño_lote <- riesgo_euros / (riesgo_pips * 10)  # Aproximación
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    tamaño_lote = round(tamaño_lote, 2),
    riesgo_euros = round(riesgo_euros, 2),
    ganancia_potencial = round(riesgo_euros * ESTRATEGIA_CONFIG$reward_risk_ratio, 2),
    riesgo_pips = round(riesgo_pips, 1),
    ganancia_pips = ESTRATEGIA_CONFIG$take_profit_pips
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE MONITOREO
# =============================================================================

monitoreo_triple_confirmacion <- function() {
  cat("🎯 MONITOREO SCALPING: TRIPLE CONFIRMACIÓN\n")
  cat("=========================================\n")
  cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
  cat("Estrategia: EMA 8/21 + RSI 14 + Bollinger Bands\n\n")
  
  if (ESTRATEGIA_CONFIG$api_key == "DEMO") {
    cat("⚠️ USANDO API KEY DEMO - DATOS LIMITADOS\n\n")
  }
  
  señales_fuertes <- list()
  
  for (i in 1:length(ESTRATEGIA_CONFIG$pares_scalping)) {
    nombre_par <- names(ESTRATEGIA_CONFIG$pares_scalping)[i]
    simbolo <- ESTRATEGIA_CONFIG$pares_scalping[[i]]
    
    cat("📊", nombre_par, "(", simbolo, ")\n")
    cat(paste(rep("-", 30), collapse = ""), "\n")
    
    # Obtener datos
    datos <- obtener_datos_scalping(simbolo)
    
    if (is.null(datos)) {
      cat("❌ Sin datos disponibles\n\n")
      next
    }
    
    # Calcular indicadores
    datos_con_indicadores <- calcular_indicadores_triple(datos)
    
    if (is.null(datos_con_indicadores)) {
      cat("❌ Error calculando indicadores\n\n")
      next
    }
    
    # Analizar señales
    analisis <- analizar_triple_confirmacion(datos_con_indicadores)
    
    # Mostrar resultados
    cat("🔍 Señal:", analisis$señal, "\n")
    cat("💪 Fuerza:", analisis$fuerza, "confirmaciones\n")
    cat("💰 Precio:", sprintf("%.5f", analisis$precio), "\n")
    cat("📊 Volatilidad:", sprintf("%.3f%%", analisis$volatilidad), "\n")
    
    # Mostrar detalles de confirmaciones
    cat("📋 Confirmaciones:\n")
    cat("   EMA:", analisis$detalles$ema$señal, "\n")
    cat("   RSI:", analisis$detalles$rsi$señal, sprintf("(%.1f)", analisis$detalles$rsi$valor), "\n")
    cat("   BB:", analisis$detalles$bollinger$señal, sprintf("(Pos: %.2f)", analisis$detalles$bollinger$posicion), "\n")
    
    # Si hay señal fuerte, calcular niveles
    if (analisis$fuerza >= ESTRATEGIA_CONFIG$min_confirmaciones) {
      cat("⭐ SEÑAL FUERTE DETECTADA!\n")
      
      niveles <- calcular_niveles_scalping(analisis$precio, analisis$señal, simbolo)
      
      if (!is.null(niveles)) {
        cat("🎯 NIVELES DE TRADING:\n")
        cat("   Entrada:", sprintf("%.5f", niveles$precio_entrada), "\n")
        cat("   Stop Loss:", sprintf("%.5f", niveles$stop_loss), "\n")
        cat("   Take Profit:", sprintf("%.5f", niveles$take_profit), "\n")
        cat("   Tamaño:", niveles$tamaño_lote, "lotes\n")
        cat("   Riesgo:", niveles$riesgo_euros, "€\n")
        cat("   Ganancia potencial:", niveles$ganancia_potencial, "€\n")
        
        señales_fuertes[[nombre_par]] <- list(
          analisis = analisis,
          niveles = niveles
        )
      }
    }
    
    cat("\n")
    
    # Pausa para límites de API
    if (i < length(ESTRATEGIA_CONFIG$pares_scalping)) {
      cat("⏳ Pausa API (12s)...\n")
      Sys.sleep(12)
    }
  }
  
  # Resumen final
  cat(paste(rep("=", 50), collapse = ""), "\n")
  cat("📊 RESUMEN FINAL\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")
  
  if (length(señales_fuertes) == 0) {
    cat("😴 No hay señales fuertes en este momento\n")
    cat("💡 Recomendación: Revisar en 5-10 minutos\n")
  } else {
    cat("🚨 SEÑALES FUERTES:", length(señales_fuertes), "\n\n")
    
    for (par in names(señales_fuertes)) {
      señal_data <- señales_fuertes[[par]]
      cat("🎯", par, "- Señal:", señal_data$analisis$señal, "\n")
      cat("   Confirmaciones:", señal_data$analisis$fuerza, "\n")
      cat("   Entrada:", sprintf("%.5f", señal_data$niveles$precio_entrada), "\n")
      cat("   R:R = 1:", ESTRATEGIA_CONFIG$reward_risk_ratio, "\n\n")
    }
  }
  
  cat("⏰ Próxima revisión en 5-10 minutos\n")
  cat("🔄 Para monitoreo continuo, ejecutar periódicamente\n")
  
  return(señales_fuertes)
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES:\n")
cat("==================\n")
cat("1. Configurar API key: ESTRATEGIA_CONFIG$api_key <- 'TU_API_KEY'\n")
cat("2. Ejecutar: señales <- monitoreo_triple_confirmacion()\n")
cat("3. Repetir cada 5-10 minutos durante sesión de trading\n\n")

cat("🎯 REGLAS DE LA ESTRATEGIA:\n")
cat("• Mínimo 2 confirmaciones de 3 indicadores\n")
cat("• Stop Loss: 8 pips | Take Profit: 16 pips (1:2)\n")
cat("• Máximo 2 operaciones simultáneas\n")
cat("• Riesgo: 0.5% por operación\n\n")
