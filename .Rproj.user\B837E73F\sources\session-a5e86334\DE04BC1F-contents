# =============================================================================
# DIAGNÓSTICO PROFUNDO - VER EXACTAMENTE QUÉ DEVUELVE LA API
# =============================================================================

library(httr)
library(jsonlite)

cat("🔍 DIAGNÓSTICO PROFUNDO ALPHA VANTAGE\n")
cat("=====================================\n")
cat("Vamos a ver exactamente qué está devolviendo la API\n\n")

api_key <- "KDF6QXHG5UOYNHJK"

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESPUESTA COMPLETA
# =============================================================================

mostrar_respuesta_completa <- function(funcion, parametros_extra = "") {
  cat("🔍 Probando función:", funcion, "\n")
  
  if (funcion == "CURRENCY_EXCHANGE_RATE") {
    url <- paste0(
      "https://www.alphavantage.co/query",
      "?function=CURRENCY_EXCHANGE_RATE",
      "&from_currency=EUR",
      "&to_currency=USD",
      "&apikey=", api_key
    )
  } else if (funcion == "FX_INTRADAY") {
    url <- paste0(
      "https://www.alphavantage.co/query",
      "?function=FX_INTRADAY",
      "&from_symbol=EUR",
      "&to_symbol=USD",
      "&interval=5min",
      "&outputsize=compact",
      "&apikey=", api_key,
      parametros_extra
    )
  } else if (funcion == "FX_DAILY") {
    url <- paste0(
      "https://www.alphavantage.co/query",
      "?function=FX_DAILY",
      "&from_symbol=EUR",
      "&to_symbol=USD",
      "&outputsize=compact",
      "&apikey=", api_key
    )
  }
  
  cat("📡 URL:", substr(url, 1, 100), "...\n")
  
  tryCatch({
    # Realizar petición
    start_time <- Sys.time()
    response <- GET(url, timeout(30))
    end_time <- Sys.time()
    
    cat("⏱️ Tiempo respuesta:", round(as.numeric(end_time - start_time), 2), "segundos\n")
    cat("📊 Status HTTP:", status_code(response), "\n")
    
    if (status_code(response) == 200) {
      # Obtener contenido raw
      content_raw <- content(response, "text", encoding = "UTF-8")
      cat("📏 Tamaño respuesta:", nchar(content_raw), "caracteres\n")
      
      # Mostrar primeros 500 caracteres
      cat("📄 CONTENIDO RAW (primeros 500 chars):\n")
      cat(paste(rep("-", 50), collapse = ""), "\n")
      cat(substr(content_raw, 1, 500), "\n")
      cat(paste(rep("-", 50), collapse = ""), "\n")
      
      # Intentar parsear JSON
      tryCatch({
        data <- fromJSON(content_raw)
        cat("✅ JSON parseado correctamente\n")
        cat("📋 CLAVES PRINCIPALES:\n")
        for (key in names(data)) {
          cat("   •", key, "\n")
        }
        
        # Mostrar estructura completa
        cat("\n📊 ESTRUCTURA COMPLETA:\n")
        str(data, max.level = 2)
        
        return(data)
        
      }, error = function(e) {
        cat("❌ Error parseando JSON:", e$message, "\n")
        cat("📄 Contenido completo:\n")
        cat(content_raw, "\n")
        return(NULL)
      })
      
    } else {
      cat("❌ Error HTTP:", status_code(response), "\n")
      cat("📄 Contenido error:\n")
      print(content(response))
      return(NULL)
    }
    
  }, error = function(e) {
    cat("❌ Error en petición:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# VERIFICAR ESTADO DE LA API KEY
# =============================================================================

cat("🔑 VERIFICANDO ESTADO DE API KEY\n")
cat("=================================\n")

# Prueba 1: Función más simple
cat("\n1️⃣ PRUEBA: CURRENCY_EXCHANGE_RATE\n")
resultado1 <- mostrar_respuesta_completa("CURRENCY_EXCHANGE_RATE")

cat("\n⏳ Esperando 15 segundos...\n")
Sys.sleep(15)

# Prueba 2: Datos diarios (más estables)
cat("\n2️⃣ PRUEBA: FX_DAILY\n")
resultado2 <- mostrar_respuesta_completa("FX_DAILY")

cat("\n⏳ Esperando 15 segundos...\n")
Sys.sleep(15)

# Prueba 3: Datos intraday
cat("\n3️⃣ PRUEBA: FX_INTRADAY\n")
resultado3 <- mostrar_respuesta_completa("FX_INTRADAY")

# =============================================================================
# ANÁLISIS DE RESULTADOS
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📊 ANÁLISIS DE RESULTADOS\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

# Verificar si alguna función funcionó
funciono_alguna <- FALSE

if (!is.null(resultado1)) {
  if ("Realtime Currency Exchange Rate" %in% names(resultado1)) {
    cat("✅ CURRENCY_EXCHANGE_RATE: FUNCIONANDO\n")
    rate_data <- resultado1$`Realtime Currency Exchange Rate`
    cat("   💱 EUR/USD:", rate_data$`5. Exchange Rate`, "\n")
    funciono_alguna <- TRUE
  } else if ("Error Message" %in% names(resultado1)) {
    cat("❌ CURRENCY_EXCHANGE_RATE: Error -", resultado1$`Error Message`, "\n")
  } else if ("Note" %in% names(resultado1)) {
    cat("⚠️ CURRENCY_EXCHANGE_RATE: Límite -", resultado1$Note, "\n")
  }
}

if (!is.null(resultado2)) {
  if ("Time Series FX (Daily)" %in% names(resultado2)) {
    cat("✅ FX_DAILY: FUNCIONANDO\n")
    daily_data <- resultado2$`Time Series FX (Daily)`
    cat("   📊 Registros diarios:", length(daily_data), "\n")
    funciono_alguna <- TRUE
  } else if ("Error Message" %in% names(resultado2)) {
    cat("❌ FX_DAILY: Error -", resultado2$`Error Message`, "\n")
  } else if ("Note" %in% names(resultado2)) {
    cat("⚠️ FX_DAILY: Límite -", resultado2$Note, "\n")
  }
}

if (!is.null(resultado3)) {
  if ("Time Series FX (5min)" %in% names(resultado3)) {
    cat("✅ FX_INTRADAY: FUNCIONANDO\n")
    intraday_data <- resultado3$`Time Series FX (5min)`
    cat("   📊 Registros intraday:", length(intraday_data), "\n")
    funciono_alguna <- TRUE
  } else if ("Error Message" %in% names(resultado3)) {
    cat("❌ FX_INTRADAY: Error -", resultado3$`Error Message`, "\n")
  } else if ("Note" %in% names(resultado3)) {
    cat("⚠️ FX_INTRADAY: Límite -", resultado3$Note, "\n")
  }
}

# =============================================================================
# RECOMENDACIONES BASADAS EN RESULTADOS
# =============================================================================

cat("\n📋 DIAGNÓSTICO FINAL:\n")
cat("======================\n")

if (funciono_alguna) {
  cat("🎉 ¡TU API KEY ESTÁ FUNCIONANDO!\n")
  cat("✅ Al menos una función devuelve datos válidos\n\n")
  
  if (!is.null(resultado2) && "Time Series FX (Daily)" %in% names(resultado2)) {
    cat("💡 SOLUCIÓN TEMPORAL: Usar datos diarios\n")
    cat("   • Mientras se estabiliza el servicio intraday\n")
    cat("   • Perfecto para análisis de tendencias\n")
    cat("   • Menos llamadas a la API\n\n")
    
    cat("🚀 CREAR ESTRATEGIA CON DATOS DIARIOS:\n")
    cat("   • Timeframe: Diario\n")
    cat("   • Señales: Más confiables\n")
    cat("   • Ideal para swing trading\n")
  }
  
  if (!is.null(resultado1) && "Realtime Currency Exchange Rate" %in% names(resultado1)) {
    cat("💡 ALTERNATIVA: Monitoreo de precios en tiempo real\n")
    cat("   • Usar CURRENCY_EXCHANGE_RATE cada minuto\n")
    cat("   • Calcular cambios porcentuales\n")
    cat("   • Detectar movimientos significativos\n")
  }
  
} else {
  cat("⚠️ PROBLEMAS DETECTADOS:\n")
  
  # Verificar errores comunes
  error_comun <- FALSE
  
  if (!is.null(resultado1) && "Note" %in% names(resultado1)) {
    cat("🚫 LÍMITE DE API ALCANZADO\n")
    cat("   • Has usado las 5 llamadas por minuto\n")
    cat("   • Espera 60 segundos y vuelve a intentar\n")
    cat("   • Considera plan premium para más llamadas\n")
    error_comun <- TRUE
  }
  
  if (!is.null(resultado1) && "Error Message" %in% names(resultado1)) {
    cat("🚫 ERROR DE API KEY\n")
    cat("   • API key inválida o no activada\n")
    cat("   • Verifica email de confirmación\n")
    cat("   • Puede tardar hasta 24 horas en activarse\n")
    error_comun <- TRUE
  }
  
  if (!error_comun) {
    cat("🚫 PROBLEMA DE FORMATO O CONECTIVIDAD\n")
    cat("   • Respuesta inesperada del servidor\n")
    cat("   • Posible mantenimiento de Alpha Vantage\n")
    cat("   • Verificar conexión a internet\n")
  }
}

cat("\n🎯 PRÓXIMOS PASOS:\n")
if (funciono_alguna) {
  cat("1. Usar la función que funciona para crear estrategia adaptada\n")
  cat("2. Esperar a que se estabilice el servicio intraday\n")
  cat("3. Monitorear durante horarios de mercado activo\n")
} else {
  cat("1. Esperar 60 minutos y volver a probar\n")
  cat("2. Verificar email de confirmación de Alpha Vantage\n")
  cat("3. Contactar soporte si persiste después de 24 horas\n")
}

cat("\n⏰ Diagnóstico completado:", format(Sys.time(), "%H:%M:%S"), "\n")
cat("📧 Si necesitas ayuda: <EMAIL>\n")
