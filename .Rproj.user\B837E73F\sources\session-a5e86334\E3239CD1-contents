# =============================================================================
# DEBUG FORMATO RESPUESTA - VER QUÉ ESTÁ DEVOLVIENDO ALPHA VANTAGE
# =============================================================================

library(httr)
library(jsonlite)

cat("🔍 DEBUG FORMATO RESPUESTA\n")
cat("==========================\n")
cat("Vamos a ver exactamente qué devuelve Alpha Vantage ahora\n\n")

api_key <- "KDF6QXHG5UOYNHJK"

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESPUESTA COMPLETA
# =============================================================================

debug_respuesta_completa <- function() {
  cat("📡 Probando CURRENCY_EXCHANGE_RATE para EUR/USD...\n")
  
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=CURRENCY_EXCHANGE_RATE",
    "&from_currency=EUR",
    "&to_currency=USD",
    "&apikey=", api_key
  )
  
  cat("🔗 URL completa:\n", url, "\n\n")
  
  tryCatch({
    # Realizar petición
    cat("⏳ Realizando petición...\n")
    response <- GET(url, timeout(30))
    
    cat("📊 Status HTTP:", status_code(response), "\n")
    cat("📏 Headers:\n")
    print(headers(response))
    
    if (status_code(response) == 200) {
      # Obtener contenido raw
      content_raw <- content(response, "text", encoding = "UTF-8")
      cat("\n📄 CONTENIDO COMPLETO:\n")
      cat(paste(rep("=", 80), collapse = ""), "\n")
      cat(content_raw, "\n")
      cat(paste(rep("=", 80), collapse = ""), "\n")
      
      # Verificar si es JSON válido
      cat("\n🔍 ANÁLISIS DEL CONTENIDO:\n")
      cat("• Longitud:", nchar(content_raw), "caracteres\n")
      cat("• Primeros 10 chars:", substr(content_raw, 1, 10), "\n")
      cat("• Últimos 10 chars:", substr(content_raw, nchar(content_raw)-9, nchar(content_raw)), "\n")
      
      # Intentar parsear JSON
      tryCatch({
        data <- fromJSON(content_raw)
        cat("✅ JSON válido parseado\n")
        cat("📋 Estructura:\n")
        str(data)
        
        cat("\n🔍 CLAVES ENCONTRADAS:\n")
        for (key in names(data)) {
          cat("   •", key, "\n")
          if (is.list(data[[key]])) {
            cat("     Subclaves:", paste(names(data[[key]]), collapse = ", "), "\n")
          } else {
            cat("     Valor:", data[[key]], "\n")
          }
        }
        
        return(data)
        
      }, error = function(e) {
        cat("❌ Error parseando JSON:", e$message, "\n")
        cat("📄 Contenido raw para análisis manual:\n")
        cat(content_raw, "\n")
        return(NULL)
      })
      
    } else {
      cat("❌ Error HTTP:", status_code(response), "\n")
      cat("📄 Contenido del error:\n")
      print(content(response))
      return(NULL)
    }
    
  }, error = function(e) {
    cat("❌ Error en petición HTTP:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# VERIFICAR ESTADO DE LA API KEY
# =============================================================================

verificar_estado_api <- function() {
  cat("\n🔑 VERIFICANDO ESTADO DE API KEY\n")
  cat("================================\n")
  
  # Probar con función más simple
  url_simple <- paste0(
    "https://www.alphavantage.co/query",
    "?function=TIME_SERIES_INTRADAY",
    "&symbol=IBM",
    "&interval=5min",
    "&apikey=", api_key
  )
  
  cat("📡 Probando con función de acciones (TIME_SERIES_INTRADAY)...\n")
  
  tryCatch({
    response <- GET(url_simple, timeout(20))
    
    if (status_code(response) == 200) {
      content_raw <- content(response, "text", encoding = "UTF-8")
      data <- fromJSON(content_raw)
      
      cat("📊 Respuesta de acciones:\n")
      cat("• Claves:", paste(names(data), collapse = ", "), "\n")
      
      if ("Error Message" %in% names(data)) {
        cat("❌ Error en acciones:", data$`Error Message`, "\n")
      } else if ("Note" %in% names(data)) {
        cat("⚠️ Límite en acciones:", data$Note, "\n")
      } else if ("Information" %in% names(data)) {
        cat("ℹ️ Información:", data$Information, "\n")
      } else {
        cat("✅ Función de acciones responde correctamente\n")
      }
    }
    
  }, error = function(e) {
    cat("❌ Error en función de acciones:", e$message, "\n")
  })
}

# =============================================================================
# PROBAR DIFERENTES VARIACIONES
# =============================================================================

probar_variaciones <- function() {
  cat("\n🧪 PROBANDO VARIACIONES DE FOREX\n")
  cat("=================================\n")
  
  # Variación 1: Con outputsize
  cat("1️⃣ Probando con outputsize=compact...\n")
  url1 <- paste0(
    "https://www.alphavantage.co/query",
    "?function=CURRENCY_EXCHANGE_RATE",
    "&from_currency=EUR",
    "&to_currency=USD",
    "&outputsize=compact",
    "&apikey=", api_key
  )
  
  probar_url(url1, "Con outputsize")
  
  Sys.sleep(10)
  
  # Variación 2: Diferente par
  cat("\n2️⃣ Probando con GBP/USD...\n")
  url2 <- paste0(
    "https://www.alphavantage.co/query",
    "?function=CURRENCY_EXCHANGE_RATE",
    "&from_currency=GBP",
    "&to_currency=USD",
    "&apikey=", api_key
  )
  
  probar_url(url2, "GBP/USD")
  
  Sys.sleep(10)
  
  # Variación 3: FX_DAILY
  cat("\n3️⃣ Probando FX_DAILY...\n")
  url3 <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_DAILY",
    "&from_symbol=EUR",
    "&to_symbol=USD",
    "&apikey=", api_key
  )
  
  probar_url(url3, "FX_DAILY")
}

probar_url <- function(url, descripcion) {
  tryCatch({
    response <- GET(url, timeout(20))
    
    if (status_code(response) == 200) {
      content_raw <- content(response, "text", encoding = "UTF-8")
      
      cat("   Status:", status_code(response), "\n")
      cat("   Tamaño:", nchar(content_raw), "chars\n")
      cat("   Inicio:", substr(content_raw, 1, 50), "...\n")
      
      if (nchar(content_raw) > 50) {
        data <- fromJSON(content_raw)
        cat("   Claves:", paste(names(data), collapse = ", "), "\n")
        
        if ("Error Message" %in% names(data)) {
          cat("   ❌ Error:", data$`Error Message`, "\n")
        } else if ("Note" %in% names(data)) {
          cat("   ⚠️ Límite:", data$Note, "\n")
        } else if ("Information" %in% names(data)) {
          cat("   ℹ️ Info:", substr(data$Information, 1, 50), "...\n")
        } else {
          cat("   ✅ Respuesta válida\n")
        }
      }
    } else {
      cat("   ❌ HTTP:", status_code(response), "\n")
    }
    
  }, error = function(e) {
    cat("   ❌ Error:", e$message, "\n")
  })
}

# =============================================================================
# EJECUTAR DIAGNÓSTICO COMPLETO
# =============================================================================

cat("🚀 INICIANDO DIAGNÓSTICO COMPLETO\n")
cat("==================================\n")

# Paso 1: Debug respuesta completa
resultado1 <- debug_respuesta_completa()

# Paso 2: Verificar estado API
verificar_estado_api()

# Paso 3: Probar variaciones
probar_variaciones()

# =============================================================================
# ANÁLISIS FINAL Y RECOMENDACIONES
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📊 ANÁLISIS FINAL\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

if (!is.null(resultado1)) {
  cat("✅ La API key funciona básicamente\n")
  cat("🔍 Revisar estructura de respuesta arriba\n")
  cat("💡 Posible cambio en formato de Alpha Vantage\n")
} else {
  cat("❌ Problema fundamental con la API\n")
  cat("🔍 Revisar contenido raw arriba\n")
  cat("💡 Posibles causas:\n")
  cat("   • API key suspendida o expirada\n")
  cat("   • Cambio en endpoints de Alpha Vantage\n")
  cat("   • Problema de conectividad\n")
  cat("   • Mantenimiento del servicio\n")
}

cat("\n🎯 PRÓXIMOS PASOS:\n")
cat("1. Revisar el contenido completo mostrado arriba\n")
cat("2. Verificar si hay mensajes de error específicos\n")
cat("3. Comprobar email de Alpha Vantage por notificaciones\n")
cat("4. Considerar crear nueva API key si es necesario\n")

cat("\n📧 SOPORTE:\n")
cat("• Alpha Vantage: <EMAIL>\n")
cat("• Documentación: https://www.alphavantage.co/documentation\n")
cat("• Estado del servicio: https://status.alphavantage.co\n\n")

cat("⏰ Diagnóstico completado:", format(Sys.time(), "%H:%M:%S"), "\n")
