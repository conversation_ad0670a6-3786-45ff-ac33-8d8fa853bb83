# =============================================================================
# CONFIGURACIÓN ALPHA VANTAGE - INSTALACIÓN Y SETUP
# =============================================================================
# Script para instalar paquetes y configurar Alpha Vantage
# Ejecutar UNA VEZ antes de usar el sistema de scalping
# =============================================================================

cat("🔧 CONFIGURACIÓN ALPHA VANTAGE SCALPING\n")
cat("=======================================\n")
cat("Instalando paquetes y configurando sistema...\n\n")

# =============================================================================
# INSTALACIÓN DE PAQUETES NECESARIOS
# =============================================================================

paquetes_necesarios <- c(
  "httr",        # Para peticiones HTTP a API
  "jsonlite",    # Para parsear respuestas JSON
  "quantmod",    # Para análisis financiero
  "TTR",         # Para indicadores técnicos
  "dplyr",       # Para manipulación de datos
  "lubridate",   # Para manejo de fechas
  "ggplot2",     # Para gráficos (opcional)
  "plotly"       # Para gráficos interactivos (opcional)
)

cat("📦 Verificando e instalando paquetes necesarios...\n")

for (paquete in paquetes_necesarios) {
  if (!require(paquete, character.only = TRUE, quietly = TRUE)) {
    cat("📥 Instalando", paquete, "...\n")
    install.packages(paquete, dependencies = TRUE, quiet = TRUE)
    
    if (require(paquete, character.only = TRUE, quietly = TRUE)) {
      cat("✅", paquete, "instalado correctamente\n")
    } else {
      cat("❌ Error instalando", paquete, "\n")
    }
  } else {
    cat("✅", paquete, "ya está instalado\n")
  }
}

cat("\n📋 Instalación de paquetes completada\n\n")

# =============================================================================
# VERIFICACIÓN DE CONEXIÓN A INTERNET
# =============================================================================

cat("🌐 Verificando conexión a internet...\n")

tryCatch({
  response <- httr::GET("https://www.alphavantage.co", timeout(10))
  if (httr::status_code(response) == 200) {
    cat("✅ Conexión a Alpha Vantage exitosa\n")
  } else {
    cat("⚠️ Respuesta inesperada de Alpha Vantage:", httr::status_code(response), "\n")
  }
}, error = function(e) {
  cat("❌ Error de conexión:", e$message, "\n")
  cat("💡 Verificar conexión a internet\n")
})

# =============================================================================
# CONFIGURACIÓN DE API KEY
# =============================================================================

cat("\n🔑 CONFIGURACIÓN DE API KEY\n")
cat("===========================\n")

# Función para configurar API key
configurar_api_key <- function() {
  cat("Para obtener datos en tiempo real necesitas una API key de Alpha Vantage\n\n")
  
  cat("📝 PASOS PARA OBTENER API KEY GRATUITA:\n")
  cat("1. Visita: https://www.alphavantage.co/support/#api-key\n")
  cat("2. Completa el formulario (nombre, email, etc.)\n")
  cat("3. Recibirás la API key por email\n")
  cat("4. Copia la API key y pégala aquí\n\n")
  
  cat("⚠️ IMPORTANTE:\n")
  cat("• Plan gratuito: 5 llamadas/minuto, 500/día\n")
  cat("• Datos con retraso de ~15 minutos\n")
  cat("• Para scalping real considera plan premium\n\n")
  
  # Intentar leer API key existente
  if (file.exists("alpha_vantage_config.txt")) {
    api_key_guardada <- readLines("alpha_vantage_config.txt", warn = FALSE)[1]
    if (nchar(api_key_guardada) > 10) {
      cat("✅ API key encontrada en archivo de configuración\n")
      cat("🔑 API key:", substr(api_key_guardada, 1, 8), "...\n")
      
      respuesta <- readline("¿Usar esta API key? (s/n): ")
      if (tolower(respuesta) %in% c("s", "si", "yes", "y")) {
        return(api_key_guardada)
      }
    }
  }
  
  # Solicitar nueva API key
  cat("\n📝 Ingresa tu API key de Alpha Vantage:\n")
  api_key <- readline("API Key: ")
  
  if (nchar(api_key) < 10) {
    cat("❌ API key muy corta. Usando DEMO por ahora.\n")
    cat("💡 Puedes configurarla después editando alpha_vantage_scalping.R\n")
    return("DEMO")
  }
  
  # Guardar API key
  tryCatch({
    writeLines(api_key, "alpha_vantage_config.txt")
    cat("✅ API key guardada en alpha_vantage_config.txt\n")
  }, error = function(e) {
    cat("⚠️ No se pudo guardar la API key:", e$message, "\n")
  })
  
  return(api_key)
}

# Configurar API key
api_key <- configurar_api_key()

# =============================================================================
# PRUEBA DE CONEXIÓN CON API
# =============================================================================

cat("\n🧪 PROBANDO CONEXIÓN CON API...\n")
cat("===============================\n")

probar_api <- function(api_key) {
  if (api_key == "DEMO") {
    cat("⚠️ Usando API key DEMO - datos limitados\n")
  }
  
  # URL de prueba
  url_prueba <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_INTRADAY",
    "&from_symbol=EUR",
    "&to_symbol=USD", 
    "&interval=1min",
    "&outputsize=compact",
    "&apikey=", api_key
  )
  
  cat("📡 Realizando petición de prueba...\n")
  
  tryCatch({
    response <- httr::GET(url_prueba, timeout(30))
    
    if (httr::status_code(response) != 200) {
      cat("❌ Error HTTP:", httr::status_code(response), "\n")
      return(FALSE)
    }
    
    data <- jsonlite::fromJSON(httr::content(response, "text"))
    
    if ("Error Message" %in% names(data)) {
      cat("❌ Error de API:", data$`Error Message`, "\n")
      return(FALSE)
    }
    
    if ("Note" %in% names(data)) {
      cat("⚠️ Límite de API:", data$Note, "\n")
      cat("💡 Espera un minuto y vuelve a intentar\n")
      return(FALSE)
    }
    
    if ("Time Series FX (1min)" %in% names(data)) {
      time_series <- data$`Time Series FX (1min)`
      cat("✅ Conexión exitosa!\n")
      cat("📊 Datos recibidos:", length(time_series), "registros\n")
      
      # Mostrar último precio
      if (length(time_series) > 0) {
        ultimo_tiempo <- names(time_series)[1]
        ultimo_precio <- time_series[[1]]$`4. close`
        cat("💰 EUR/USD último precio:", ultimo_precio, "\n")
        cat("⏰ Última actualización:", ultimo_tiempo, "\n")
      }
      
      return(TRUE)
    } else {
      cat("❌ Respuesta inesperada de la API\n")
      return(FALSE)
    }
    
  }, error = function(e) {
    cat("❌ Error en la prueba:", e$message, "\n")
    return(FALSE)
  })
}

# Ejecutar prueba
exito_prueba <- probar_api(api_key)

# =============================================================================
# ACTUALIZAR CONFIGURACIÓN EN ARCHIVO PRINCIPAL
# =============================================================================

if (exito_prueba && api_key != "DEMO") {
  cat("\n🔧 Actualizando configuración en alpha_vantage_scalping.R...\n")
  
  tryCatch({
    # Leer archivo principal
    if (file.exists("alpha_vantage_scalping.R")) {
      lineas <- readLines("alpha_vantage_scalping.R")
      
      # Buscar y reemplazar línea de API key
      for (i in 1:length(lineas)) {
        if (grepl('api_key = "DEMO"', lineas[i])) {
          lineas[i] <- paste0('  api_key = "', api_key, '",  # ✅ Configurada automáticamente')
          break
        }
      }
      
      # Escribir archivo actualizado
      writeLines(lineas, "alpha_vantage_scalping.R")
      cat("✅ API key actualizada en archivo principal\n")
    }
  }, error = function(e) {
    cat("⚠️ No se pudo actualizar automáticamente:", e$message, "\n")
    cat("💡 Edita manualmente alpha_vantage_scalping.R\n")
  })
}

# =============================================================================
# RESUMEN FINAL
# =============================================================================

cat("\n", paste(rep("=", 50), collapse = ""), "\n")
cat("🎉 CONFIGURACIÓN COMPLETADA\n")
cat(paste(rep("=", 50), collapse = ""), "\n")

if (exito_prueba) {
  cat("✅ Sistema listo para usar\n")
  cat("🚀 Ejecuta: source('alpha_vantage_scalping.R')\n")
  cat("🎯 Luego: monitoreo_scalping_tiempo_real()\n")
} else {
  cat("⚠️ Configuración parcial completada\n")
  cat("🔧 Revisa la API key y conexión\n")
  cat("💡 Puedes usar modo DEMO con datos limitados\n")
}

cat("\n📚 PRÓXIMOS PASOS:\n")
cat("1. source('alpha_vantage_scalping.R')\n")
cat("2. monitoreo_scalping_tiempo_real()\n")
cat("3. Para monitoreo continuo, ejecutar cada 1-5 minutos\n")

cat("\n💡 CONSEJOS:\n")
cat("• Plan gratuito: 5 llamadas/min, 500/día\n")
cat("• Para scalping real considera plan premium\n")
cat("• Monitorea durante horarios de mayor volatilidad\n")
cat("• Combina con análisis técnico manual\n\n")

cat("🔗 RECURSOS ÚTILES:\n")
cat("• Alpha Vantage: https://www.alphavantage.co\n")
cat("• Documentación API: https://www.alphavantage.co/documentation\n")
cat("• Planes premium: https://www.alphavantage.co/premium\n\n")
