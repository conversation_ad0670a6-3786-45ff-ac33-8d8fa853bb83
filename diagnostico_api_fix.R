# =============================================================================
# DIAGNÓSTICO Y SOLUCIÓN DE PROBLEMAS API ALPHA VANTAGE
# =============================================================================
# Script para diagnosticar y solucionar problemas comunes con Alpha Vantage
# =============================================================================

library(httr)
library(jsonlite)

cat("🔧 DIAGNÓSTICO API ALPHA VANTAGE\n")
cat("================================\n")
cat("Solucionando problema de 'Respuesta inesperada'\n\n")

# Cargar API key
api_key <- "KDF6QXHG5UOYNHJK"  # Tu API key
cat("🔑 API Key cargada:", substr(api_key, 1, 8), "...\n\n")

# =============================================================================
# DIAGNÓSTICO PASO A PASO
# =============================================================================

cat("🔍 PASO 1: Verificando conectividad básica...\n")
tryCatch({
  response <- GET("https://www.alphavantage.co", timeout(10))
  cat("✅ Conectividad a Alpha Vantage: OK (", status_code(response), ")\n")
}, error = function(e) {
  cat("❌ Error de conectividad:", e$message, "\n")
})

cat("\n🔍 PASO 2: Probando API con función básica...\n")

# URL de prueba más simple
url_simple <- paste0(
  "https://www.alphavantage.co/query",
  "?function=CURRENCY_EXCHANGE_RATE",
  "&from_currency=USD",
  "&to_currency=EUR",
  "&apikey=", api_key
)

cat("📡 URL de prueba:", substr(url_simple, 1, 80), "...\n")

tryCatch({
  response <- GET(url_simple, timeout(30))
  status <- status_code(response)
  cat("📊 Status HTTP:", status, "\n")
  
  if (status == 200) {
    content_raw <- content(response, "text", encoding = "UTF-8")
    cat("📄 Contenido recibido (primeros 200 chars):\n")
    cat(substr(content_raw, 1, 200), "...\n\n")
    
    # Intentar parsear JSON
    tryCatch({
      data <- fromJSON(content_raw)
      cat("✅ JSON parseado correctamente\n")
      cat("📋 Claves disponibles:", paste(names(data), collapse = ", "), "\n")
      
      # Verificar contenido específico
      if ("Realtime Currency Exchange Rate" %in% names(data)) {
        exchange_data <- data$`Realtime Currency Exchange Rate`
        cat("💱 Tipo de cambio USD/EUR:", exchange_data$`5. Exchange Rate`, "\n")
        cat("⏰ Última actualización:", exchange_data$`6. Last Refreshed`, "\n")
        cat("🎉 API FUNCIONANDO CORRECTAMENTE!\n")
        
      } else if ("Error Message" %in% names(data)) {
        cat("❌ Error de API:", data$`Error Message`, "\n")
        
      } else if ("Note" %in% names(data)) {
        cat("⚠️ Límite de API:", data$Note, "\n")
        cat("💡 Solución: Esperar 1 minuto y volver a intentar\n")
        
      } else {
        cat("⚠️ Respuesta inesperada. Contenido completo:\n")
        print(data)
      }
      
    }, error = function(e) {
      cat("❌ Error parseando JSON:", e$message, "\n")
      cat("📄 Contenido raw:\n", content_raw, "\n")
    })
    
  } else {
    cat("❌ Error HTTP:", status, "\n")
  }
  
}, error = function(e) {
  cat("❌ Error en petición:", e$message, "\n")
})

cat("\n🔍 PASO 3: Probando función FX_INTRADAY...\n")

# Esperar un poco para respetar límites
cat("⏳ Esperando 15 segundos para respetar límites de API...\n")
Sys.sleep(15)

url_fx <- paste0(
  "https://www.alphavantage.co/query",
  "?function=FX_INTRADAY",
  "&from_symbol=EUR",
  "&to_symbol=USD",
  "&interval=5min",
  "&outputsize=compact",
  "&apikey=", api_key
)

tryCatch({
  response <- GET(url_fx, timeout(30))
  status <- status_code(response)
  cat("📊 Status HTTP FX:", status, "\n")
  
  if (status == 200) {
    content_raw <- content(response, "text", encoding = "UTF-8")
    
    tryCatch({
      data <- fromJSON(content_raw)
      cat("✅ JSON FX parseado correctamente\n")
      cat("📋 Claves FX disponibles:", paste(names(data), collapse = ", "), "\n")
      
      if ("Time Series FX (5min)" %in% names(data)) {
        time_series <- data$`Time Series FX (5min)`
        cat("📈 Datos FX obtenidos:", length(time_series), "registros\n")
        
        if (length(time_series) > 0) {
          ultimo_tiempo <- names(time_series)[1]
          ultimo_precio <- time_series[[1]]$`4. close`
          cat("💰 EUR/USD último precio:", ultimo_precio, "\n")
          cat("⏰ Última actualización:", ultimo_tiempo, "\n")
          cat("🎉 DATOS FX FUNCIONANDO CORRECTAMENTE!\n")
        }
        
      } else if ("Error Message" %in% names(data)) {
        cat("❌ Error FX:", data$`Error Message`, "\n")
        
      } else if ("Note" %in% names(data)) {
        cat("⚠️ Límite FX:", data$Note, "\n")
        
      } else {
        cat("⚠️ Respuesta FX inesperada:\n")
        print(names(data))
      }
      
    }, error = function(e) {
      cat("❌ Error parseando JSON FX:", e$message, "\n")
    })
  }
  
}, error = function(e) {
  cat("❌ Error en petición FX:", e$message, "\n")
})

# =============================================================================
# ACTUALIZAR CONFIGURACIÓN CON API KEY VÁLIDA
# =============================================================================

cat("\n🔧 ACTUALIZANDO CONFIGURACIÓN...\n")

# Actualizar archivo principal de estrategia
if (file.exists("estrategia_scalping_triple_confirmacion.R")) {
  tryCatch({
    lineas <- readLines("estrategia_scalping_triple_confirmacion.R")
    
    for (i in 1:length(lineas)) {
      if (grepl('api_key = "DEMO"', lineas[i])) {
        lineas[i] <- paste0('  api_key = "', api_key, '",  # ✅ API key configurada')
        break
      }
    }
    
    writeLines(lineas, "estrategia_scalping_triple_confirmacion.R")
    cat("✅ API key actualizada en estrategia_scalping_triple_confirmacion.R\n")
    
  }, error = function(e) {
    cat("⚠️ No se pudo actualizar automáticamente:", e$message, "\n")
  })
}

# Actualizar archivo base
if (file.exists("alpha_vantage_scalping.R")) {
  tryCatch({
    lineas <- readLines("alpha_vantage_scalping.R")
    
    for (i in 1:length(lineas)) {
      if (grepl('api_key = "DEMO"', lineas[i])) {
        lineas[i] <- paste0('  api_key = "', api_key, '",  # ✅ API key configurada')
        break
      }
    }
    
    writeLines(lineas, "alpha_vantage_scalping.R")
    cat("✅ API key actualizada en alpha_vantage_scalping.R\n")
    
  }, error = function(e) {
    cat("⚠️ No se pudo actualizar automáticamente:", e$message, "\n")
  })
}

# =============================================================================
# PRUEBA FINAL CON CONFIGURACIÓN ACTUALIZADA
# =============================================================================

cat("\n🎯 PRUEBA FINAL DEL SISTEMA...\n")
cat("==============================\n")

if (file.exists("estrategia_scalping_triple_confirmacion.R")) {
  cat("📥 Cargando estrategia actualizada...\n")
  source("estrategia_scalping_triple_confirmacion.R")
  
  cat("🔍 Probando obtención de datos EUR/USD...\n")
  datos_test <- obtener_datos_scalping("EURUSD")
  
  if (!is.null(datos_test) && nrow(datos_test) > 0) {
    cat("🎉 ¡SISTEMA FUNCIONANDO PERFECTAMENTE!\n")
    cat("📊 Datos obtenidos:", nrow(datos_test), "registros\n")
    cat("💰 Último precio EUR/USD:", sprintf("%.5f", datos_test$Close[1]), "\n")
    cat("⏰ Última actualización:", format(datos_test$DateTime[1], "%Y-%m-%d %H:%M:%S"), "\n")
    
    cat("\n🚀 LISTO PARA SCALPING!\n")
    cat("Ejecuta: monitoreo_triple_confirmacion()\n")
    
  } else {
    cat("⚠️ Aún hay problemas con los datos\n")
    cat("💡 Posibles causas:\n")
    cat("   • Límite de API alcanzado (esperar 1 minuto)\n")
    cat("   • API key nueva (puede tardar unos minutos en activarse)\n")
    cat("   • Problema temporal de Alpha Vantage\n")
  }
}

# =============================================================================
# RECOMENDACIONES FINALES
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📋 RECOMENDACIONES FINALES\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

cat("✅ PASOS COMPLETADOS:\n")
cat("• API key configurada y guardada\n")
cat("• Archivos actualizados con tu API key\n")
cat("• Conectividad verificada\n\n")

cat("🎯 PRÓXIMOS PASOS:\n")
cat("1. Esperar 2-3 minutos (API keys nuevas pueden tardar)\n")
cat("2. Ejecutar: source('estrategia_scalping_triple_confirmacion.R')\n")
cat("3. Probar: monitoreo_triple_confirmacion()\n")
cat("4. Si funciona: source('sistema_alertas_scalping.R')\n\n")

cat("⚠️ SI AÚN HAY PROBLEMAS:\n")
cat("• Verificar que la API key esté activa (revisar email)\n")
cat("• Esperar 5-10 minutos y volver a intentar\n")
cat("• Verificar límites: 5 llamadas/min, 500/día\n")
cat("• Contactar soporte Alpha Vantage si persiste\n\n")

cat("💡 MIENTRAS TANTO:\n")
cat("• Puedes revisar la estrategia en el código\n")
cat("• Estudiar los indicadores (EMA, RSI, Bollinger)\n")
cat("• Preparar tu plan de trading\n\n")

cat("🔗 RECURSOS:\n")
cat("• Documentación: https://www.alphavantage.co/documentation\n")
cat("• Soporte: https://www.alphavantage.co/support\n")
cat("• Estado del servicio: https://status.alphavantage.co\n\n")
