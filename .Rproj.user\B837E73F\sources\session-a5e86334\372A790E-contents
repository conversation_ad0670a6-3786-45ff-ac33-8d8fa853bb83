# =============================================================================
# INICIO RÁPIDO - SISTEMA SCALPING YAHOO FINANCE
# =============================================================================
# Script de inicio rápido para el sistema completo de scalping
# =============================================================================

cat("🚀 INICIO RÁPIDO - SISTEMA SCALPING\n")
cat("===================================\n")
cat("Yahoo Finance • Sin límites • Guía XTB\n\n")

# =============================================================================
# VERIFICACIÓN RÁPIDA DEL SISTEMA
# =============================================================================

verificar_instalacion <- function() {
  cat("🔍 VERIFICANDO INSTALACIÓN...\n")
  
  # Verificar archivos principales
  archivos_principales <- c(
    "scalping_master.R",
    "estrategia_scalping_yahoo.R",
    "sistema_alertas_yahoo.R",
    "test_yahoo_connection.R",
    "setup_scalping_yahoo.R",
    "calculadora_riesgo.R"
  )
  
  archivos_ok <- 0
  for (archivo in archivos_principales) {
    if (file.exists(archivo)) {
      archivos_ok <- archivos_ok + 1
      cat("✅", archivo, "\n")
    } else {
      cat("❌", archivo, "- FALTANTE\n")
    }
  }
  
  # Verificar directorios
  directorios <- c("logs", "data", "config")
  for (dir in directorios) {
    if (dir.exists(dir)) {
      cat("✅ Directorio", dir, "\n")
    } else {
      cat("⚠️ Directorio", dir, "- Creando...\n")
      dir.create(dir, recursive = TRUE)
    }
  }
  
  cat("\n📊 ESTADO: ", archivos_ok, "/", length(archivos_principales), "archivos OK\n")
  
  if (archivos_ok == length(archivos_principales)) {
    cat("🎉 ¡SISTEMA COMPLETO!\n")
    return(TRUE)
  } else {
    cat("⚠️ Sistema incompleto\n")
    return(FALSE)
  }
}

# =============================================================================
# INSTALACIÓN AUTOMÁTICA
# =============================================================================

instalacion_automatica <- function() {
  cat("\n🔧 INSTALACIÓN AUTOMÁTICA\n")
  cat("=========================\n")
  
  if (file.exists("setup_scalping_yahoo.R")) {
    cat("📦 Ejecutando setup automático...\n")
    source("setup_scalping_yahoo.R")
    cat("✅ Setup completado\n")
  } else {
    cat("❌ Archivo setup no encontrado\n")
    return(FALSE)
  }
  
  return(TRUE)
}

# =============================================================================
# PRUEBA RÁPIDA
# =============================================================================

prueba_rapida <- function() {
  cat("\n🧪 PRUEBA RÁPIDA DEL SISTEMA\n")
  cat("============================\n")
  
  if (file.exists("test_yahoo_connection.R")) {
    source("test_yahoo_connection.R")
  } else {
    cat("❌ Archivo de prueba no encontrado\n")
  }
}

# =============================================================================
# MENÚ DE INICIO RÁPIDO
# =============================================================================

mostrar_menu_inicio <- function() {
  cat("\n", paste(rep("=", 50), collapse = ""), "\n")
  cat("🎯 MENÚ INICIO RÁPIDO\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")
  cat("1. 🔧 Instalación completa (primera vez)\n")
  cat("2. 🧪 Prueba rápida de conexión\n")
  cat("3. ⚡ Análisis scalping express\n")
  cat("4. 🎯 Sistema completo (menú principal)\n")
  cat("5. 💰 Calculadora de riesgo\n")
  cat("6. 📚 Ver documentación\n")
  cat("7. 🚪 Salir\n")
  cat(paste(rep("-", 50), collapse = ""), "\n")
  cat("Selecciona una opción (1-7): ")
}

# =============================================================================
# FUNCIONES DEL MENÚ DE INICIO
# =============================================================================

opcion_instalacion <- function() {
  cat("\n🔧 INSTALACIÓN COMPLETA\n")
  cat("=======================\n")
  
  if (!verificar_instalacion()) {
    cat("💡 Ejecutando instalación automática...\n")
    if (instalacion_automatica()) {
      cat("✅ Instalación completada\n")
      prueba_rapida()
    } else {
      cat("❌ Error en instalación\n")
    }
  } else {
    cat("✅ Sistema ya instalado correctamente\n")
  }
}

opcion_prueba <- function() {
  cat("\n🧪 PRUEBA RÁPIDA\n")
  cat("================\n")
  prueba_rapida()
}

opcion_express <- function() {
  cat("\n⚡ ANÁLISIS EXPRESS\n")
  cat("==================\n")
  
  if (file.exists("estrategia_scalping_yahoo.R")) {
    source("estrategia_scalping_yahoo.R")
    scalping_express()
  } else {
    cat("❌ Estrategia no encontrada\n")
    cat("💡 Ejecuta primero la instalación (opción 1)\n")
  }
}

opcion_sistema_completo <- function() {
  cat("\n🎯 SISTEMA COMPLETO\n")
  cat("===================\n")
  
  if (file.exists("scalping_master.R")) {
    source("scalping_master.R")
    ejecutar_scalping_master()
  } else {
    cat("❌ Sistema principal no encontrado\n")
    cat("💡 Ejecuta primero la instalación (opción 1)\n")
  }
}

opcion_calculadora <- function() {
  cat("\n💰 CALCULADORA DE RIESGO\n")
  cat("========================\n")
  
  if (file.exists("calculadora_riesgo.R")) {
    source("calculadora_riesgo.R")
    calculadora_interactiva()
  } else {
    cat("❌ Calculadora no encontrada\n")
    cat("💡 Ejecuta primero la instalación (opción 1)\n")
  }
}

opcion_documentacion <- function() {
  cat("\n📚 DOCUMENTACIÓN\n")
  cat("================\n")
  
  if (file.exists("README.md")) {
    cat("📖 Abriendo README.md...\n")
    tryCatch({
      file.show("README.md")
    }, error = function(e) {
      cat("⚠️ No se pudo abrir automáticamente\n")
      cat("💡 Abre manualmente el archivo README.md\n")
    })
  } else {
    cat("❌ README.md no encontrado\n")
  }
  
  cat("\n📋 ARCHIVOS DE DOCUMENTACIÓN:\n")
  cat("• README.md - Guía completa del sistema\n")
  cat("• INICIO_RAPIDO.R - Este archivo\n")
  cat("• logs/sistema.log - Log de instalación\n\n")
  
  cat("🎯 FUNCIONES PRINCIPALES:\n")
  cat("• analisis_scalping_yahoo() - Análisis completo\n")
  cat("• scalping_express() - Análisis rápido\n")
  cat("• calculadora_interactiva() - Calcular posiciones\n")
  cat("• ejecutar_scalping_master() - Sistema completo\n\n")
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE INICIO RÁPIDO
# =============================================================================

inicio_rapido <- function() {
  cat("🎉 Bienvenido al Sistema de Scalping\n")
  cat("Versión: 2.0 | Yahoo Finance | Sin límites\n\n")
  
  # Verificación inicial
  sistema_ok <- verificar_instalacion()
  
  if (!sistema_ok) {
    cat("\n⚠️ SISTEMA INCOMPLETO DETECTADO\n")
    cat("💡 Recomendación: Ejecutar instalación completa (opción 1)\n")
  }
  
  # Bucle del menú
  while (TRUE) {
    mostrar_menu_inicio()
    opcion <- readline()
    
    switch(opcion,
      "1" = opcion_instalacion(),
      "2" = opcion_prueba(),
      "3" = opcion_express(),
      "4" = opcion_sistema_completo(),
      "5" = opcion_calculadora(),
      "6" = opcion_documentacion(),
      "7" = {
        cat("\n👋 ¡Hasta luego! Happy Trading!\n")
        cat("💡 Para volver a iniciar: source('INICIO_RAPIDO.R'); inicio_rapido()\n")
        break
      },
      {
        cat("\n❌ Opción no válida. Selecciona 1-7.\n")
      }
    )
    
    if (opcion != "7") {
      cat("\nPresiona Enter para continuar...")
      readline()
    }
  }
}

# =============================================================================
# FUNCIONES DE ACCESO DIRECTO
# =============================================================================

# Acceso directo al análisis
analisis_directo <- function() {
  if (file.exists("estrategia_scalping_yahoo.R")) {
    source("estrategia_scalping_yahoo.R")
    return(analisis_scalping_yahoo())
  } else {
    cat("❌ Sistema no instalado. Ejecuta: inicio_rapido()\n")
    return(NULL)
  }
}

# Acceso directo a alertas
alertas_directo <- function() {
  if (file.exists("sistema_alertas_yahoo.R")) {
    source("sistema_alertas_yahoo.R")
    monitoreo_manual_alertas()
  } else {
    cat("❌ Sistema no instalado. Ejecuta: inicio_rapido()\n")
  }
}

# Acceso directo a calculadora
calc_directo <- function() {
  if (file.exists("calculadora_riesgo.R")) {
    source("calculadora_riesgo.R")
    return(calc_demo_estandar())
  } else {
    cat("❌ Sistema no instalado. Ejecuta: inicio_rapido()\n")
    return(NULL)
  }
}

# =============================================================================
# INFORMACIÓN INICIAL
# =============================================================================

cat("🎯 SISTEMA LISTO PARA USAR\n")
cat("===========================\n")
cat("Funciones disponibles:\n")
cat("• inicio_rapido() - Menú principal de inicio\n")
cat("• analisis_directo() - Análisis inmediato\n")
cat("• alertas_directo() - Monitoreo con alertas\n")
cat("• calc_directo() - Calculadora rápida\n\n")

cat("🚀 INICIO RECOMENDADO:\n")
cat("1. Primera vez: inicio_rapido() → opción 1 (instalación)\n")
cat("2. Uso normal: inicio_rapido() → opción 3 (análisis express)\n")
cat("3. Sistema completo: inicio_rapido() → opción 4\n\n")

cat("💡 CONSEJOS:\n")
cat("• Siempre probar en cuenta demo primero\n")
cat("• Verificar conexión antes de operar\n")
cat("• Usar gestión de riesgo conservadora\n")
cat("• Combinar con análisis fundamental\n\n")

# Auto-ejecutar si se desea
cat("¿Iniciar el menú de inicio rápido ahora? (s/n): ")
respuesta <- readline()
if (tolower(respuesta) == "s") {
  inicio_rapido()
} else {
  cat("\n💡 Para iniciar manualmente: inicio_rapido()\n")
  cat("📚 Para ver documentación: file.show('README.md')\n")
}
