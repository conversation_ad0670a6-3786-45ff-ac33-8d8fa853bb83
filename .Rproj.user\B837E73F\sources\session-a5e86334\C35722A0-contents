# =============================================================================
# SISTEMA DE ALERTAS AUTOMÁTICAS PARA SCALPING
# =============================================================================
# Sistema que monitorea continuamente y genera alertas cuando detecta
# señales fuertes de la estrategia Triple Confirmación
# =============================================================================

library(beepr)  # Para sonidos de alerta
library(tcltk)  # Para ventanas emergentes

cat("🚨 SISTEMA DE ALERTAS SCALPING\n")
cat("==============================\n")
cat("Monitoreo automático con alertas visuales y sonoras\n\n")

# =============================================================================
# CONFIGURACIÓN DE ALERTAS
# =============================================================================

ALERTAS_CONFIG <- list(
  # Configuración de monitoreo
  intervalo_monitoreo_minutos = 5,     # Revisar cada 5 minutos
  max_ciclos_monitoreo = 100,          # Máximo 100 ciclos (8+ horas)
  
  # Configuración de alertas
  alertas_sonoras = TRUE,              # Activar sonidos
  alertas_visuales = TRUE,             # Activar ventanas emergentes
  alertas_archivo = TRUE,              # Guardar en archivo
  
  # Filtros de alertas
  fuerza_minima_alerta = 3,           # Mínimo 3 confirmaciones
  volatilidad_minima = 0.5,           # Mínimo 0.5% volatilidad
  
  # Configuración de archivos
  archivo_alertas = "alertas_scalping.txt",
  archivo_log = "log_monitoreo.txt",
  
  # Horarios de trading (UTC)
  hora_inicio_trading = 7,             # 7:00 UTC (apertura Londres)
  hora_fin_trading = 21,               # 21:00 UTC (cierre NY)
  
  # Días de trading (1=Lunes, 7=Domingo)
  dias_trading = c(1, 2, 3, 4, 5)     # Lunes a Viernes
)

# =============================================================================
# FUNCIONES DE ALERTAS
# =============================================================================

# Verificar si estamos en horario de trading
en_horario_trading <- function() {
  ahora <- Sys.time()
  hora_actual <- as.numeric(format(ahora, "%H"))
  dia_semana <- as.numeric(format(ahora, "%u"))  # 1=Lunes, 7=Domingo
  
  hora_ok <- hora_actual >= ALERTAS_CONFIG$hora_inicio_trading && 
             hora_actual <= ALERTAS_CONFIG$hora_fin_trading
  dia_ok <- dia_semana %in% ALERTAS_CONFIG$dias_trading
  
  return(hora_ok && dia_ok)
}

# Función para reproducir sonido de alerta
reproducir_alerta_sonora <- function(tipo = "señal") {
  if (!ALERTAS_CONFIG$alertas_sonoras) return()
  
  tryCatch({
    if (tipo == "señal") {
      beepr::beep(sound = 2)  # Sonido de moneda
    } else if (tipo == "error") {
      beepr::beep(sound = 9)  # Sonido de error
    } else if (tipo == "inicio") {
      beepr::beep(sound = 1)  # Sonido simple
    }
  }, error = function(e) {
    cat("⚠️ No se pudo reproducir sonido:", e$message, "\n")
  })
}

# Función para mostrar alerta visual
mostrar_alerta_visual <- function(titulo, mensaje) {
  if (!ALERTAS_CONFIG$alertas_visuales) return()
  
  tryCatch({
    tcltk::tk_messageBox(
      type = "ok",
      message = mensaje,
      title = titulo,
      icon = "warning"
    )
  }, error = function(e) {
    cat("⚠️ No se pudo mostrar ventana emergente:", e$message, "\n")
  })
}

# Función para escribir en archivo de alertas
escribir_alerta_archivo <- function(par, señal_info) {
  if (!ALERTAS_CONFIG$alertas_archivo) return()
  
  tryCatch({
    timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
    
    mensaje <- paste0(
      "=== ALERTA SCALPING ===\n",
      "Fecha/Hora: ", timestamp, "\n",
      "Par: ", par, "\n",
      "Señal: ", señal_info$analisis$señal, "\n",
      "Fuerza: ", señal_info$analisis$fuerza, " confirmaciones\n",
      "Precio: ", sprintf("%.5f", señal_info$analisis$precio), "\n",
      "Volatilidad: ", sprintf("%.3f%%", señal_info$analisis$volatilidad), "\n",
      "Stop Loss: ", sprintf("%.5f", señal_info$niveles$stop_loss), "\n",
      "Take Profit: ", sprintf("%.5f", señal_info$niveles$take_profit), "\n",
      "Tamaño: ", señal_info$niveles$tamaño_lote, " lotes\n",
      "Riesgo: ", señal_info$niveles$riesgo_euros, " EUR\n",
      "Ganancia potencial: ", señal_info$niveles$ganancia_potencial, " EUR\n",
      "========================\n\n"
    )
    
    cat(mensaje, file = ALERTAS_CONFIG$archivo_alertas, append = TRUE)
    
  }, error = function(e) {
    cat("⚠️ Error escribiendo archivo:", e$message, "\n")
  })
}

# Función para escribir log de monitoreo
escribir_log <- function(mensaje) {
  tryCatch({
    timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
    log_entry <- paste0("[", timestamp, "] ", mensaje, "\n")
    cat(log_entry, file = ALERTAS_CONFIG$archivo_log, append = TRUE)
  }, error = function(e) {
    # Silencioso si no se puede escribir log
  })
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE MONITOREO CON ALERTAS
# =============================================================================

monitoreo_automatico_con_alertas <- function() {
  cat("🤖 INICIANDO MONITOREO AUTOMÁTICO\n")
  cat("=================================\n")
  cat("Intervalo:", ALERTAS_CONFIG$intervalo_monitoreo_minutos, "minutos\n")
  cat("Máximo ciclos:", ALERTAS_CONFIG$max_ciclos_monitoreo, "\n")
  cat("Horario trading: ", ALERTAS_CONFIG$hora_inicio_trading, ":00 - ", 
      ALERTAS_CONFIG$hora_fin_trading, ":00 UTC\n")
  cat("Días trading: Lunes a Viernes\n\n")
  
  # Verificar dependencias
  if (ALERTAS_CONFIG$alertas_sonoras) {
    if (!require(beepr, quietly = TRUE)) {
      cat("⚠️ Paquete 'beepr' no disponible. Instalando...\n")
      install.packages("beepr")
    }
  }
  
  if (ALERTAS_CONFIG$alertas_visuales) {
    if (!require(tcltk, quietly = TRUE)) {
      cat("⚠️ Paquete 'tcltk' no disponible. Alertas visuales desactivadas.\n")
      ALERTAS_CONFIG$alertas_visuales <- FALSE
    }
  }
  
  # Cargar estrategia si no está cargada
  if (!exists("monitoreo_triple_confirmacion")) {
    cat("📥 Cargando estrategia Triple Confirmación...\n")
    source("estrategia_scalping_triple_confirmacion.R")
  }
  
  # Sonido de inicio
  reproducir_alerta_sonora("inicio")
  escribir_log("Monitoreo automático iniciado")
  
  ciclo <- 1
  alertas_enviadas <- 0
  
  while (ciclo <= ALERTAS_CONFIG$max_ciclos_monitoreo) {
    cat("\n🔄 CICLO", ciclo, "de", ALERTAS_CONFIG$max_ciclos_monitoreo, "\n")
    cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
    
    # Verificar horario de trading
    if (!en_horario_trading()) {
      cat("😴 Fuera de horario de trading. Esperando...\n")
      escribir_log("Fuera de horario de trading")
      
      # Esperar hasta el próximo horario de trading
      Sys.sleep(30 * 60)  # 30 minutos
      ciclo <- ciclo + 1
      next
    }
    
    cat("✅ En horario de trading. Monitoreando...\n")
    
    tryCatch({
      # Ejecutar monitoreo
      señales_detectadas <- monitoreo_triple_confirmacion()
      
      # Procesar señales
      if (length(señales_detectadas) > 0) {
        cat("🚨 SEÑALES DETECTADAS:", length(señales_detectadas), "\n")
        
        for (par in names(señales_detectadas)) {
          señal_info <- señales_detectadas[[par]]
          
          # Verificar filtros de alerta
          if (señal_info$analisis$fuerza >= ALERTAS_CONFIG$fuerza_minima_alerta &&
              señal_info$analisis$volatilidad >= ALERTAS_CONFIG$volatilidad_minima) {
            
            cat("⭐ ENVIANDO ALERTA PARA", par, "\n")
            
            # Preparar mensaje de alerta
            mensaje_alerta <- paste0(
              "🚨 SEÑAL SCALPING DETECTADA 🚨\n\n",
              "Par: ", par, "\n",
              "Señal: ", señal_info$analisis$señal, "\n",
              "Fuerza: ", señal_info$analisis$fuerza, " confirmaciones\n",
              "Precio: ", sprintf("%.5f", señal_info$analisis$precio), "\n",
              "Stop Loss: ", sprintf("%.5f", señal_info$niveles$stop_loss), "\n",
              "Take Profit: ", sprintf("%.5f", señal_info$niveles$take_profit), "\n",
              "Riesgo: ", señal_info$niveles$riesgo_euros, " EUR\n",
              "Ganancia: ", señal_info$niveles$ganancia_potencial, " EUR"
            )
            
            # Enviar alertas
            reproducir_alerta_sonora("señal")
            mostrar_alerta_visual("SEÑAL SCALPING", mensaje_alerta)
            escribir_alerta_archivo(par, señal_info)
            
            alertas_enviadas <- alertas_enviadas + 1
            escribir_log(paste("Alerta enviada para", par, "-", señal_info$analisis$señal))
          }
        }
      } else {
        cat("😴 No hay señales fuertes en este ciclo\n")
        escribir_log("Sin señales detectadas")
      }
      
    }, error = function(e) {
      cat("❌ Error en monitoreo:", e$message, "\n")
      escribir_log(paste("Error en monitoreo:", e$message))
      reproducir_alerta_sonora("error")
    })
    
    # Mostrar estadísticas
    cat("📊 Estadísticas del monitoreo:\n")
    cat("   Ciclos completados:", ciclo, "\n")
    cat("   Alertas enviadas:", alertas_enviadas, "\n")
    cat("   Próxima revisión en", ALERTAS_CONFIG$intervalo_monitoreo_minutos, "minutos\n")
    
    # Esperar hasta el próximo ciclo
    if (ciclo < ALERTAS_CONFIG$max_ciclos_monitoreo) {
      cat("⏳ Esperando", ALERTAS_CONFIG$intervalo_monitoreo_minutos, "minutos...\n")
      Sys.sleep(ALERTAS_CONFIG$intervalo_monitoreo_minutos * 60)
    }
    
    ciclo <- ciclo + 1
  }
  
  cat("\n🏁 MONITOREO COMPLETADO\n")
  cat("Total ciclos:", ciclo - 1, "\n")
  cat("Total alertas:", alertas_enviadas, "\n")
  escribir_log("Monitoreo automático finalizado")
}

# =============================================================================
# FUNCIÓN PARA MONITOREO MANUAL CON ALERTAS
# =============================================================================

monitoreo_manual_con_alertas <- function() {
  cat("👤 MONITOREO MANUAL CON ALERTAS\n")
  cat("===============================\n")
  
  if (!en_horario_trading()) {
    cat("⚠️ Fuera de horario de trading\n")
    cat("Horario recomendado: ", ALERTAS_CONFIG$hora_inicio_trading, ":00 - ", 
        ALERTAS_CONFIG$hora_fin_trading, ":00 UTC\n")
    cat("¿Continuar de todos modos? (s/n): ")
    respuesta <- readline()
    if (tolower(respuesta) != "s") {
      return()
    }
  }
  
  # Cargar estrategia si no está cargada
  if (!exists("monitoreo_triple_confirmacion")) {
    source("estrategia_scalping_triple_confirmacion.R")
  }
  
  señales_detectadas <- monitoreo_triple_confirmacion()
  
  if (length(señales_detectadas) > 0) {
    for (par in names(señales_detectadas)) {
      señal_info <- señales_detectadas[[par]]
      
      if (señal_info$analisis$fuerza >= ALERTAS_CONFIG$fuerza_minima_alerta) {
        mensaje_alerta <- paste0(
          "🎯 SEÑAL MANUAL DETECTADA\n\n",
          "Par: ", par, "\n",
          "Señal: ", señal_info$analisis$señal, "\n",
          "Fuerza: ", señal_info$analisis$fuerza, " confirmaciones\n",
          "Precio: ", sprintf("%.5f", señal_info$analisis$precio)
        )
        
        reproducir_alerta_sonora("señal")
        mostrar_alerta_visual("SEÑAL MANUAL", mensaje_alerta)
        escribir_alerta_archivo(par, señal_info)
      }
    }
  }
  
  return(señales_detectadas)
}

# =============================================================================
# FUNCIONES DE CONFIGURACIÓN
# =============================================================================

configurar_alertas <- function() {
  cat("⚙️ CONFIGURACIÓN DE ALERTAS\n")
  cat("===========================\n")
  
  cat("Configuración actual:\n")
  cat("• Intervalo monitoreo:", ALERTAS_CONFIG$intervalo_monitoreo_minutos, "minutos\n")
  cat("• Alertas sonoras:", ALERTAS_CONFIG$alertas_sonoras, "\n")
  cat("• Alertas visuales:", ALERTAS_CONFIG$alertas_visuales, "\n")
  cat("• Fuerza mínima:", ALERTAS_CONFIG$fuerza_minima_alerta, "\n")
  
  cat("\n¿Modificar configuración? (s/n): ")
  respuesta <- readline()
  
  if (tolower(respuesta) == "s") {
    cat("Nuevo intervalo en minutos (actual", ALERTAS_CONFIG$intervalo_monitoreo_minutos, "): ")
    nuevo_intervalo <- readline()
    if (nuevo_intervalo != "") {
      ALERTAS_CONFIG$intervalo_monitoreo_minutos <<- as.numeric(nuevo_intervalo)
    }
    
    cat("Activar alertas sonoras? (s/n): ")
    sonoras <- readline()
    ALERTAS_CONFIG$alertas_sonoras <<- tolower(sonoras) == "s"
    
    cat("Activar alertas visuales? (s/n): ")
    visuales <- readline()
    ALERTAS_CONFIG$alertas_visuales <<- tolower(visuales) == "s"
    
    cat("✅ Configuración actualizada\n")
  }
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Monitoreo automático: monitoreo_automatico_con_alertas()\n")
cat("• Monitoreo manual: monitoreo_manual_con_alertas()\n")
cat("• Configurar alertas: configurar_alertas()\n\n")

cat("🔧 CONFIGURACIÓN INICIAL:\n")
cat("• Instalar paquetes: install.packages(c('beepr', 'tcltk'))\n")
cat("• Configurar API key en estrategia_scalping_triple_confirmacion.R\n")
cat("• Ajustar horarios de trading según tu zona horaria\n\n")

cat("📁 ARCHIVOS GENERADOS:\n")
cat("• ", ALERTAS_CONFIG$archivo_alertas, " - Registro de alertas\n")
cat("• ", ALERTAS_CONFIG$archivo_log, " - Log de monitoreo\n\n")

cat("⚠️ IMPORTANTE:\n")
cat("• El monitoreo automático puede ejecutarse durante horas\n")
cat("• Verificar conexión a internet estable\n")
cat("• Respetar límites de API de Alpha Vantage\n")
cat("• Usar en horarios de alta volatilidad del mercado\n\n")
