# =============================================================================
# TEST CONEXIÓN YAHOO FINANCE - VERIFICACIÓN COMPLETA
# =============================================================================
# Prueba exhaustiva de conexión y funcionalidad con Yahoo Finance
# =============================================================================

library(quantmod)
library(TTR)

cat("🧪 TEST CONEXIÓN YAHOO FINANCE\n")
cat("==============================\n")
cat("Verificación completa del sistema\n\n")

# =============================================================================
# CONFIGURACIÓN DE PRUEBA
# =============================================================================

pares_test <- c(
  "EUR/USD" = "EURUSD=X",
  "GBP/USD" = "GBPUSD=X", 
  "USD/JPY" = "USDJPY=X",
  "AUD/USD" = "AUDUSD=X"
)

# =============================================================================
# FUNCIÓN DE PRUEBA INDIVIDUAL
# =============================================================================

probar_par <- function(nombre, simbolo) {
  cat("📊 Probando", nombre, "(", simbolo, ")...")
  
  tryCatch({
    # Obtener datos de los últimos 10 días
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = Sys.Date() - 10,
                       to = Sys.Date(),
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    # Extraer información básica
    ultimo_precio <- as.numeric(tail(Cl(datos), 1))
    precio_anterior <- as.numeric(tail(Cl(datos), 2)[1])
    cambio <- ultimo_precio - precio_anterior
    cambio_pct <- (cambio / precio_anterior) * 100
    
    cat(" ✅ OK\n")
    cat("   💰 Precio actual:", sprintf("%.5f", ultimo_precio), "\n")
    cat("   📊 Cambio:", sprintf("%+.5f", cambio), sprintf("(%+.3f%%)", cambio_pct), "\n")
    cat("   📅 Registros:", nrow(datos), "días\n")
    cat("   ⏰ Última actualización:", format(tail(index(datos), 1), "%Y-%m-%d"), "\n")
    
    return(list(
      simbolo = simbolo,
      precio = ultimo_precio,
      cambio_pct = cambio_pct,
      registros = nrow(datos),
      datos = datos
    ))
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# PRUEBA DE TODOS LOS PARES
# =============================================================================

cat("🔍 PROBANDO TODOS LOS PARES\n")
cat("============================\n")

resultados <- list()
pares_exitosos <- 0

for (i in 1:length(pares_test)) {
  nombre <- names(pares_test)[i]
  simbolo <- pares_test[[i]]
  
  resultado <- probar_par(nombre, simbolo)
  
  if (!is.null(resultado)) {
    resultados[[nombre]] <- resultado
    pares_exitosos <- pares_exitosos + 1
  }
  
  cat("\n")
  
  # Pausa pequeña entre pruebas
  if (i < length(pares_test)) {
    Sys.sleep(1)
  }
}

# =============================================================================
# PRUEBA DE INDICADORES TÉCNICOS
# =============================================================================

cat("📈 PROBANDO INDICADORES TÉCNICOS\n")
cat("=================================\n")

if (length(resultados) > 0) {
  # Usar el primer par exitoso para probar indicadores
  primer_par <- resultados[[1]]
  datos_test <- primer_par$datos
  
  cat("📊 Usando", names(resultados)[1], "para probar indicadores...\n")
  
  tryCatch({
    # Calcular indicadores
    precios <- Cl(datos_test)
    
    cat("📈 Calculando EMA...")
    ema8 <- EMA(precios, n = 8)
    ema21 <- EMA(precios, n = 21)
    cat(" ✅ OK\n")
    
    cat("📊 Calculando RSI...")
    rsi <- RSI(precios, n = 14)
    cat(" ✅ OK\n")
    
    cat("📉 Calculando Bollinger Bands...")
    bb <- BBands(precios, n = 20, sd = 2)
    cat(" ✅ OK\n")
    
    # Mostrar valores actuales
    ultimo_ema8 <- as.numeric(tail(ema8, 1))
    ultimo_ema21 <- as.numeric(tail(ema21, 1))
    ultimo_rsi <- as.numeric(tail(rsi, 1))
    
    cat("\n📊 VALORES ACTUALES DE INDICADORES:\n")
    cat("   EMA 8:", sprintf("%.5f", ultimo_ema8), "\n")
    cat("   EMA 21:", sprintf("%.5f", ultimo_ema21), "\n")
    cat("   RSI:", sprintf("%.2f", ultimo_rsi), "\n")
    
    # Análisis básico
    if (ultimo_ema8 > ultimo_ema21) {
      cat("   🔼 Tendencia: ALCISTA (EMA 8 > EMA 21)\n")
    } else {
      cat("   🔽 Tendencia: BAJISTA (EMA 8 < EMA 21)\n")
    }
    
    if (ultimo_rsi > 70) {
      cat("   ⚠️ RSI: SOBRECOMPRA (", sprintf("%.2f", ultimo_rsi), ")\n")
    } else if (ultimo_rsi < 30) {
      cat("   ⚠️ RSI: SOBREVENTA (", sprintf("%.2f", ultimo_rsi), ")\n")
    } else {
      cat("   ✅ RSI: NEUTRAL (", sprintf("%.2f", ultimo_rsi), ")\n")
    }
    
    cat("✅ Todos los indicadores funcionan correctamente\n")
    
  }, error = function(e) {
    cat("❌ Error calculando indicadores:", e$message, "\n")
  })
  
} else {
  cat("❌ No hay datos para probar indicadores\n")
}

# =============================================================================
# PRUEBA DE VELOCIDAD
# =============================================================================

cat("\n⚡ PRUEBA DE VELOCIDAD\n")
cat("=====================\n")

if (length(resultados) > 0) {
  cat("📊 Midiendo velocidad de obtención de datos...\n")
  
  start_time <- Sys.time()
  
  # Obtener datos de EUR/USD
  datos_velocidad <- getSymbols("EURUSD=X", 
                               src = "yahoo",
                               from = Sys.Date() - 30,
                               to = Sys.Date(),
                               auto.assign = FALSE,
                               warnings = FALSE)
  
  end_time <- Sys.time()
  tiempo_transcurrido <- as.numeric(end_time - start_time)
  
  cat("⏱️ Tiempo para 30 días de EUR/USD:", round(tiempo_transcurrido, 2), "segundos\n")
  
  if (tiempo_transcurrido < 5) {
    cat("✅ Velocidad: EXCELENTE\n")
  } else if (tiempo_transcurrido < 10) {
    cat("✅ Velocidad: BUENA\n")
  } else {
    cat("⚠️ Velocidad: LENTA (verificar conexión)\n")
  }
}

# =============================================================================
# PRUEBA DE HORARIOS DE MERCADO
# =============================================================================

cat("\n🕐 VERIFICANDO HORARIOS DE MERCADO\n")
cat("==================================\n")

hora_actual <- as.numeric(format(Sys.time(), "%H"))
dia_semana <- as.numeric(format(Sys.time(), "%u"))  # 1=Lunes, 7=Domingo

cat("⏰ Hora actual (UTC):", format(Sys.time(), "%H:%M:%S"), "\n")
cat("📅 Día de la semana:", c("Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo")[dia_semana], "\n")

# Verificar si es horario de trading
if (dia_semana >= 1 && dia_semana <= 5) {  # Lunes a Viernes
  if (hora_actual >= 7 && hora_actual <= 21) {  # 7:00 - 21:00 UTC
    cat("✅ HORARIO DE TRADING ACTIVO\n")
    cat("💡 Momento ideal para hacer scalping\n")
  } else {
    cat("⚠️ Fuera de horario principal de trading\n")
    cat("💡 Mercado menos activo, menor volatilidad\n")
  }
} else {
  cat("⚠️ Fin de semana - Mercados cerrados\n")
  cat("💡 Los datos se actualizarán el lunes\n")
}

# =============================================================================
# RESUMEN FINAL
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📊 RESUMEN DE PRUEBAS\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

cat("📈 CONEXIÓN YAHOO FINANCE:\n")
cat("   • Pares probados:", length(pares_test), "\n")
cat("   • Pares exitosos:", pares_exitosos, "\n")
cat("   • Tasa de éxito:", round((pares_exitosos / length(pares_test)) * 100, 1), "%\n\n")

if (pares_exitosos >= 3) {
  cat("🎉 ¡SISTEMA COMPLETAMENTE FUNCIONAL!\n\n")
  
  cat("✅ COMPONENTES VERIFICADOS:\n")
  cat("   • Conexión a Yahoo Finance\n")
  cat("   • Obtención de datos en tiempo real\n")
  cat("   • Cálculo de indicadores técnicos\n")
  cat("   • Velocidad de respuesta\n\n")
  
  cat("🚀 LISTO PARA SCALPING:\n")
  cat("   • source('estrategia_scalping_yahoo.R')\n")
  cat("   • source('scalping_master.R')\n")
  cat("   • source('sistema_alertas_yahoo.R')\n\n")
  
  # Mostrar mejores oportunidades actuales
  if (length(resultados) > 0) {
    cat("📊 MOVIMIENTOS ACTUALES:\n")
    for (par in names(resultados)) {
      res <- resultados[[par]]
      if (abs(res$cambio_pct) > 0.1) {  # Movimientos > 0.1%
        cat("   🎯", par, ":", sprintf("%+.3f%%", res$cambio_pct), "\n")
      }
    }
  }
  
} else {
  cat("⚠️ PROBLEMAS DETECTADOS\n\n")
  
  cat("❌ POSIBLES CAUSAS:\n")
  cat("   • Conexión a internet inestable\n")
  cat("   • Problemas temporales de Yahoo Finance\n")
  cat("   • Firewall bloqueando conexiones\n\n")
  
  cat("🔧 SOLUCIONES:\n")
  cat("   • Verificar conexión a internet\n")
  cat("   • Intentar en unos minutos\n")
  cat("   • Verificar configuración de firewall\n\n")
}

cat("📝 LOGS GUARDADOS EN:\n")
cat("   • logs/sistema.log\n")
cat("   • data/test_*.rds\n\n")

cat("⏰ Prueba completada:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")

# =============================================================================
# GUARDAR RESULTADOS DE PRUEBA
# =============================================================================

if (length(resultados) > 0) {
  # Guardar resultados para uso posterior
  saveRDS(resultados, "data/test_results.rds")
  
  # Crear reporte de prueba
  reporte <- paste0(
    "=== REPORTE DE PRUEBA YAHOO FINANCE ===\n",
    "Fecha: ", Sys.time(), "\n",
    "Pares probados: ", length(pares_test), "\n",
    "Pares exitosos: ", pares_exitosos, "\n",
    "Tasa de éxito: ", round((pares_exitosos / length(pares_test)) * 100, 1), "%\n",
    "========================================\n\n"
  )
  
  writeLines(reporte, "logs/test_report.log")
  cat("💾 Resultados guardados en data/test_results.rds\n")
}
