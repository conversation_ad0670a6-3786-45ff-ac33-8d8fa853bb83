# =============================================================================
# SETUP SCALPING YAHOO FINANCE - CONFIGURACIÓN INICIAL
# =============================================================================
# Instalación y configuración completa del sistema de scalping
# =============================================================================

cat("🚀 SETUP SISTEMA SCALPING YAHOO FINANCE\n")
cat("=======================================\n")
cat("Configuración inicial del sistema completo\n\n")

# =============================================================================
# VERIFICAR E INSTALAR PAQUETES NECESARIOS
# =============================================================================

paquetes_necesarios <- c(
  "quantmod",     # Para datos de Yahoo Finance
  "TTR",          # Para indicadores técnicos
  "xts",          # Para series temporales
  "zoo",          # Para manejo de datos temporales
  "PerformanceAnalytics", # Para análisis de rendimiento
  "dplyr",        # Para manipulación de datos
  "ggplot2",      # Para gráficos
  "plotly",       # Para gráficos interactivos
  "DT",           # Para tablas interactivas
  "shiny",        # Para dashboard (opcional)
  "beepr",        # Para alertas sonoras
  "tcltk"         # Para ventanas emergentes
)

cat("📦 INSTALANDO PAQUETES NECESARIOS\n")
cat("==================================\n")

for (paquete in paquetes_necesarios) {
  cat("📥 Verificando", paquete, "...")
  
  if (!require(paquete, character.only = TRUE, quietly = TRUE)) {
    cat(" Instalando...\n")
    tryCatch({
      install.packages(paquete, dependencies = TRUE, quiet = TRUE)
      
      if (require(paquete, character.only = TRUE, quietly = TRUE)) {
        cat("   ✅", paquete, "instalado correctamente\n")
      } else {
        cat("   ❌ Error instalando", paquete, "\n")
      }
    }, error = function(e) {
      cat("   ❌ Error:", e$message, "\n")
    })
  } else {
    cat(" ✅ Ya instalado\n")
  }
}

# =============================================================================
# CONFIGURACIÓN GLOBAL DEL SISTEMA
# =============================================================================

cat("\n⚙️ CONFIGURACIÓN GLOBAL\n")
cat("=======================\n")

# Configuración de quantmod
options("getSymbols.warning4.0" = FALSE)
options("getSymbols.yahoo.warning" = FALSE)

# Configuración de zona horaria
Sys.setenv(TZ = "UTC")

cat("✅ Zona horaria configurada: UTC\n")
cat("✅ Opciones de quantmod configuradas\n")

# =============================================================================
# CREAR ESTRUCTURA DE DIRECTORIOS
# =============================================================================

cat("\n📁 CREANDO ESTRUCTURA DE DIRECTORIOS\n")
cat("=====================================\n")

directorios <- c(
  "logs",           # Para archivos de log
  "data",           # Para datos guardados
  "reports",        # Para reportes
  "backups",        # Para respaldos
  "config"          # Para configuraciones
)

for (dir in directorios) {
  if (!dir.exists(dir)) {
    dir.create(dir, recursive = TRUE)
    cat("✅ Directorio creado:", dir, "\n")
  } else {
    cat("✅ Directorio existe:", dir, "\n")
  }
}

# =============================================================================
# CONFIGURACIÓN INICIAL DEL SISTEMA
# =============================================================================

cat("\n🔧 CONFIGURACIÓN INICIAL\n")
cat("========================\n")

# Configuración por defecto
config_inicial <- list(
  # Configuración de trading
  capital_demo = 10000,
  capital_real = 5000,
  riesgo_por_operacion_pct = 1.0,
  max_operaciones_simultaneas = 2,
  
  # Pares de divisas
  pares_principales = c("EURUSD=X", "GBPUSD=X", "USDJPY=X", "AUDUSD=X"),
  pares_secundarios = c("USDCHF=X", "EURGBP=X", "GBPJPY=X"),
  
  # Indicadores técnicos
  ema_rapida = 8,
  ema_lenta = 21,
  rsi_periodo = 14,
  rsi_sobrecompra = 70,
  rsi_sobreventa = 30,
  
  # Stop Loss y Take Profit
  stop_loss_pips = 50,
  take_profit_pips = 100,
  
  # Configuración de alertas
  alertas_sonoras = TRUE,
  alertas_visuales = TRUE,
  guardar_logs = TRUE,
  
  # Horarios de trading (UTC)
  hora_inicio = 7,    # 7:00 UTC
  hora_fin = 21,      # 21:00 UTC
  dias_trading = c(1, 2, 3, 4, 5)  # Lunes a Viernes
)

# Guardar configuración
saveRDS(config_inicial, "config/config_inicial.rds")
cat("✅ Configuración inicial guardada\n")

# =============================================================================
# VERIFICAR CONEXIÓN CON YAHOO FINANCE
# =============================================================================

cat("\n🌐 VERIFICANDO CONEXIÓN YAHOO FINANCE\n")
cat("=====================================\n")

tryCatch({
  # Probar obtener datos de EUR/USD
  cat("📡 Probando conexión con EUR/USD...")
  
  datos_test <- getSymbols("EURUSD=X", 
                          src = "yahoo",
                          from = Sys.Date() - 5,
                          to = Sys.Date(),
                          auto.assign = FALSE,
                          warnings = FALSE)
  
  if (!is.null(datos_test) && nrow(datos_test) > 0) {
    ultimo_precio <- as.numeric(tail(Cl(datos_test), 1))
    cat(" ✅ Conectado\n")
    cat("💰 EUR/USD último precio:", sprintf("%.5f", ultimo_precio), "\n")
    cat("📊 Datos obtenidos:", nrow(datos_test), "registros\n")
    
    # Guardar datos de prueba
    saveRDS(datos_test, "data/test_eurusd.rds")
    cat("✅ Datos de prueba guardados\n")
    
  } else {
    cat(" ❌ Sin datos\n")
  }
  
}, error = function(e) {
  cat(" ❌ Error:", e$message, "\n")
  cat("💡 Verificar conexión a internet\n")
})

# =============================================================================
# CREAR ARCHIVO DE LOG INICIAL
# =============================================================================

cat("\n📝 CONFIGURANDO SISTEMA DE LOGS\n")
cat("===============================\n")

# Crear archivo de log inicial
log_inicial <- paste0(
  "=== SISTEMA SCALPING YAHOO FINANCE ===\n",
  "Fecha instalación: ", Sys.time(), "\n",
  "Versión R: ", R.version.string, "\n",
  "Sistema operativo: ", Sys.info()["sysname"], "\n",
  "Usuario: ", Sys.info()["user"], "\n",
  "Directorio: ", getwd(), "\n",
  "========================================\n\n"
)

writeLines(log_inicial, "logs/sistema.log")
cat("✅ Sistema de logs configurado\n")

# =============================================================================
# VERIFICAR PAQUETES CRÍTICOS
# =============================================================================

cat("\n🔍 VERIFICACIÓN FINAL DE PAQUETES\n")
cat("=================================\n")

paquetes_criticos <- c("quantmod", "TTR", "xts")
todos_ok <- TRUE

for (paquete in paquetes_criticos) {
  if (require(paquete, character.only = TRUE, quietly = TRUE)) {
    cat("✅", paquete, "- OK\n")
  } else {
    cat("❌", paquete, "- FALLO\n")
    todos_ok <- FALSE
  }
}

# =============================================================================
# RESUMEN FINAL
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📊 RESUMEN DE INSTALACIÓN\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

if (todos_ok) {
  cat("🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!\n\n")
  
  cat("✅ COMPONENTES INSTALADOS:\n")
  cat("   • Paquetes R necesarios\n")
  cat("   • Estructura de directorios\n")
  cat("   • Configuración inicial\n")
  cat("   • Conexión Yahoo Finance verificada\n")
  cat("   • Sistema de logs configurado\n\n")
  
  cat("🚀 PRÓXIMOS PASOS:\n")
  cat("1. source('test_yahoo_connection.R')  # Probar conexión\n")
  cat("2. source('estrategia_scalping_yahoo.R')  # Cargar estrategia\n")
  cat("3. source('scalping_master.R')  # Sistema completo\n\n")
  
  cat("🎯 ARCHIVOS PRINCIPALES:\n")
  cat("   • scalping_master.R - Sistema principal con menú\n")
  cat("   • estrategia_scalping_yahoo.R - Estrategia de scalping\n")
  cat("   • sistema_alertas_yahoo.R - Sistema de alertas\n")
  cat("   • guia_xtb_completa.R - Guía para XTB\n\n")
  
} else {
  cat("⚠️ INSTALACIÓN INCOMPLETA\n\n")
  cat("❌ PROBLEMAS DETECTADOS:\n")
  cat("   • Algunos paquetes críticos no se instalaron\n")
  cat("   • Verificar conexión a internet\n")
  cat("   • Intentar instalación manual de paquetes\n\n")
  
  cat("🔧 SOLUCIONES:\n")
  cat("1. install.packages(c('quantmod', 'TTR', 'xts'))\n")
  cat("2. Reiniciar R y volver a ejecutar este script\n")
  cat("3. Verificar permisos de escritura en directorio\n\n")
}

cat("📚 DOCUMENTACIÓN:\n")
cat("   • README.md - Guía completa del sistema\n")
cat("   • logs/sistema.log - Log de instalación\n")
cat("   • config/config_inicial.rds - Configuración\n\n")

cat("⏰ Instalación completada:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")

# =============================================================================
# FUNCIÓN DE AYUDA
# =============================================================================

mostrar_ayuda_inicial <- function() {
  cat("\n🆘 AYUDA RÁPIDA\n")
  cat("===============\n")
  cat("• Ver configuración: readRDS('config/config_inicial.rds')\n")
  cat("• Probar conexión: source('test_yahoo_connection.R')\n")
  cat("• Sistema principal: source('scalping_master.R')\n")
  cat("• Ver logs: readLines('logs/sistema.log')\n")
  cat("• Documentación: file.show('README.md')\n\n")
}

cat("💡 Para ver ayuda rápida: mostrar_ayuda_inicial()\n\n")
