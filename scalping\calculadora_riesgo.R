# =============================================================================
# CALCULADORA DE RIESGO Y POSICIONES - GESTIÓN DE CAPITAL
# =============================================================================
# Herramienta para calcular tamaños de posición y gestión de riesgo
# =============================================================================

cat("💰 CALCULADORA DE RIESGO Y POSICIONES\n")
cat("=====================================\n")
cat("Gestión profesional de capital para scalping\n\n")

# =============================================================================
# FUNCIÓN PRINCIPAL DE CÁLCULO
# =============================================================================

calcular_posicion <- function(capital, riesgo_pct, stop_loss_pips, par = "EURUSD", 
                             precio_entrada = NULL, tipo_cuenta = "demo") {
  
  cat("💰 CALCULADORA DE POSICIÓN\n")
  cat("==========================\n")
  cat("Par:", par, "\n")
  cat("Capital:", ifelse(tipo_cuenta == "demo", "$", ""), capital, "\n")
  cat("Riesgo:", riesgo_pct, "%\n")
  cat("Stop Loss:", stop_loss_pips, "pips\n")
  
  if (!is.null(precio_entrada)) {
    cat("Precio entrada:", sprintf("%.5f", precio_entrada), "\n")
  }
  cat("\n")
  
  # Calcular riesgo en dinero
  riesgo_dinero <- capital * (riesgo_pct / 100)
  
  # Determinar valor del pip según el par
  if (grepl("JPY", par)) {
    # Pares con JPY: pip = 0.01
    valor_pip_por_lote <- 1000  # Para 1 lote estándar
    pip_size <- 0.01
  } else {
    # Otros pares: pip = 0.0001
    valor_pip_por_lote <- 10    # Para 1 lote estándar
    pip_size <- 0.0001
  }
  
  # Calcular tamaño de posición
  # Riesgo = Tamaño_Lote * Valor_Pip * Stop_Loss_Pips
  # Tamaño_Lote = Riesgo / (Valor_Pip * Stop_Loss_Pips)
  
  tamaño_lote <- riesgo_dinero / (valor_pip_por_lote * stop_loss_pips)
  
  # Ajustar para diferentes tipos de cuenta
  if (tipo_cuenta == "demo") {
    # Cuentas demo suelen usar lotes más pequeños
    tamaño_lote_ajustado <- round(tamaño_lote / 10, 2)  # Convertir a mini lotes
    if (tamaño_lote_ajustado < 0.01) tamaño_lote_ajustado <- 0.01
  } else {
    # Cuenta real
    tamaño_lote_ajustado <- round(tamaño_lote, 2)
    if (tamaño_lote_ajustado < 0.01) tamaño_lote_ajustado <- 0.01
  }
  
  # Calcular niveles de Take Profit (ratio 1:2)
  take_profit_pips <- stop_loss_pips * 2
  ganancia_potencial <- riesgo_dinero * 2
  
  # Calcular niveles de precio si se proporciona precio de entrada
  if (!is.null(precio_entrada)) {
    if (grepl("JPY", par)) {
      sl_precio <- precio_entrada - (stop_loss_pips * 0.01)  # Para compra
      tp_precio <- precio_entrada + (take_profit_pips * 0.01)
    } else {
      sl_precio <- precio_entrada - (stop_loss_pips * 0.0001)
      tp_precio <- precio_entrada + (take_profit_pips * 0.0001)
    }
  }
  
  # Mostrar resultados
  cat("📊 RESULTADOS DEL CÁLCULO:\n")
  cat("==========================\n")
  cat("💸 Riesgo en dinero:", ifelse(tipo_cuenta == "demo", "$", ""), round(riesgo_dinero, 2), "\n")
  cat("📏 Tamaño de posición:", tamaño_lote_ajustado, "lotes\n")
  cat("📈 Take Profit:", take_profit_pips, "pips\n")
  cat("💰 Ganancia potencial:", ifelse(tipo_cuenta == "demo", "$", ""), round(ganancia_potencial, 2), "\n")
  cat("📊 Ratio Risk:Reward: 1:2\n\n")
  
  if (!is.null(precio_entrada)) {
    cat("🎯 NIVELES DE PRECIO:\n")
    cat("=====================\n")
    cat("📍 Entrada:", sprintf("%.5f", precio_entrada), "\n")
    cat("🛑 Stop Loss:", sprintf("%.5f", sl_precio), "\n")
    cat("🎯 Take Profit:", sprintf("%.5f", tp_precio), "\n\n")
  }
  
  cat("🎯 PARA XTB:\n")
  cat("============\n")
  cat("• Volumen:", tamaño_lote_ajustado, "lotes\n")
  if (!is.null(precio_entrada)) {
    cat("• Stop Loss:", sprintf("%.5f", sl_precio), "\n")
    cat("• Take Profit:", sprintf("%.5f", tp_precio), "\n")
  }
  cat("• Tipo cuenta:", toupper(tipo_cuenta), "\n\n")
  
  # Retornar resultados
  resultado <- list(
    capital = capital,
    riesgo_pct = riesgo_pct,
    riesgo_dinero = riesgo_dinero,
    stop_loss_pips = stop_loss_pips,
    take_profit_pips = take_profit_pips,
    tamaño_lote = tamaño_lote_ajustado,
    ganancia_potencial = ganancia_potencial,
    par = par
  )
  
  if (!is.null(precio_entrada)) {
    resultado$precio_entrada <- precio_entrada
    resultado$stop_loss_precio <- sl_precio
    resultado$take_profit_precio <- tp_precio
  }
  
  return(resultado)
}

# =============================================================================
# CALCULADORA INTERACTIVA
# =============================================================================

calculadora_interactiva <- function() {
  cat("🎯 CALCULADORA INTERACTIVA\n")
  cat("==========================\n")
  
  # Solicitar datos
  cat("💰 Capital de la cuenta: $")
  capital <- as.numeric(readline())
  
  cat("📊 Riesgo por operación (1-3%): ")
  riesgo <- as.numeric(readline())
  
  cat("🛑 Stop Loss en pips (30-100): ")
  sl_pips <- as.numeric(readline())
  
  cat("💱 Par de divisas (ej: EURUSD): ")
  par <- readline()
  
  cat("📍 Precio de entrada (opcional, Enter para omitir): ")
  precio_input <- readline()
  precio <- if (precio_input == "") NULL else as.numeric(precio_input)
  
  cat("🏦 Tipo de cuenta (demo/real): ")
  tipo <- readline()
  if (!(tipo %in% c("demo", "real"))) tipo <- "demo"
  
  # Calcular
  resultado <- calcular_posicion(capital, riesgo, sl_pips, par, precio, tipo)
  
  # Preguntar si quiere guardar
  cat("💾 ¿Guardar cálculo? (s/n): ")
  guardar <- readline()
  
  if (tolower(guardar) == "s") {
    timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
    archivo <- paste0("logs/calculo_", timestamp, ".rds")
    saveRDS(resultado, archivo)
    cat("✅ Cálculo guardado en:", archivo, "\n")
  }
  
  return(resultado)
}

# =============================================================================
# CALCULADORAS PREDEFINIDAS
# =============================================================================

# Calculadora para cuenta demo estándar
calc_demo_estandar <- function(par = "EURUSD", precio_entrada = NULL) {
  cat("🎮 CALCULADORA DEMO ESTÁNDAR\n")
  cat("============================\n")
  calcular_posicion(
    capital = 10000,
    riesgo_pct = 1.0,
    stop_loss_pips = 50,
    par = par,
    precio_entrada = precio_entrada,
    tipo_cuenta = "demo"
  )
}

# Calculadora para cuenta real conservadora
calc_real_conservadora <- function(capital = 5000, par = "EURUSD", precio_entrada = NULL) {
  cat("🏦 CALCULADORA REAL CONSERVADORA\n")
  cat("================================\n")
  calcular_posicion(
    capital = capital,
    riesgo_pct = 0.5,
    stop_loss_pips = 30,
    par = par,
    precio_entrada = precio_entrada,
    tipo_cuenta = "real"
  )
}

# Calculadora para scalping agresivo
calc_scalping_agresivo <- function(capital = 10000, par = "EURUSD", precio_entrada = NULL) {
  cat("⚡ CALCULADORA SCALPING AGRESIVO\n")
  cat("===============================\n")
  calcular_posicion(
    capital = capital,
    riesgo_pct = 2.0,
    stop_loss_pips = 30,
    par = par,
    precio_entrada = precio_entrada,
    tipo_cuenta = "demo"
  )
}

# =============================================================================
# ANÁLISIS DE MÚLTIPLES ESCENARIOS
# =============================================================================

analisis_escenarios <- function(capital = 10000, par = "EURUSD", precio_entrada = NULL) {
  cat("📊 ANÁLISIS DE MÚLTIPLES ESCENARIOS\n")
  cat("===================================\n")
  
  escenarios <- list(
    "Conservador" = list(riesgo = 0.5, sl = 50),
    "Moderado" = list(riesgo = 1.0, sl = 50),
    "Agresivo" = list(riesgo = 2.0, sl = 30)
  )
  
  resultados <- list()
  
  for (nombre in names(escenarios)) {
    esc <- escenarios[[nombre]]
    cat("\n🎯", nombre, ":\n")
    cat(paste(rep("-", 20), collapse = ""), "\n")
    
    resultado <- calcular_posicion(
      capital = capital,
      riesgo_pct = esc$riesgo,
      stop_loss_pips = esc$sl,
      par = par,
      precio_entrada = precio_entrada,
      tipo_cuenta = "demo"
    )
    
    resultados[[nombre]] <- resultado
  }
  
  # Resumen comparativo
  cat("\n📋 RESUMEN COMPARATIVO:\n")
  cat("=======================\n")
  cat(sprintf("%-12s %-8s %-8s %-12s %-12s\n", 
              "Escenario", "Riesgo%", "Lotes", "Riesgo$", "Ganancia$"))
  cat(paste(rep("-", 60), collapse = ""), "\n")
  
  for (nombre in names(resultados)) {
    res <- resultados[[nombre]]
    cat(sprintf("%-12s %-8.1f %-8.2f %-12.0f %-12.0f\n",
                nombre, res$riesgo_pct, res$tamaño_lote, 
                res$riesgo_dinero, res$ganancia_potencial))
  }
  
  return(resultados)
}

# =============================================================================
# CALCULADORA DE DRAWDOWN
# =============================================================================

calcular_drawdown <- function(capital_inicial, perdidas_consecutivas, riesgo_pct) {
  cat("📉 CALCULADORA DE DRAWDOWN\n")
  cat("==========================\n")
  
  capital_actual <- capital_inicial
  perdida_acumulada <- 0
  
  cat("Capital inicial: $", capital_inicial, "\n")
  cat("Riesgo por operación:", riesgo_pct, "%\n")
  cat("Pérdidas consecutivas:", perdidas_consecutivas, "\n\n")
  
  cat("📊 EVOLUCIÓN DEL CAPITAL:\n")
  cat(sprintf("%-10s %-12s %-12s %-12s\n", "Operación", "Pérdida", "Capital", "Drawdown%"))
  cat(paste(rep("-", 50), collapse = ""), "\n")
  
  for (i in 1:perdidas_consecutivas) {
    perdida_operacion <- capital_actual * (riesgo_pct / 100)
    capital_actual <- capital_actual - perdida_operacion
    perdida_acumulada <- perdida_acumulada + perdida_operacion
    drawdown_pct <- (perdida_acumulada / capital_inicial) * 100
    
    cat(sprintf("%-10d %-12.0f %-12.0f %-12.1f\n", 
                i, perdida_operacion, capital_actual, drawdown_pct))
  }
  
  cat("\n📊 RESUMEN:\n")
  cat("Pérdida total: $", round(perdida_acumulada, 0), "\n")
  cat("Capital restante: $", round(capital_actual, 0), "\n")
  cat("Drawdown máximo:", round((perdida_acumulada / capital_inicial) * 100, 1), "%\n")
  
  if (drawdown_pct > 20) {
    cat("⚠️ ALERTA: Drawdown superior al 20%\n")
    cat("💡 Considera reducir el riesgo por operación\n")
  }
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Calculadora interactiva: calculadora_interactiva()\n")
cat("• Demo estándar: calc_demo_estandar('EURUSD', 1.16580)\n")
cat("• Real conservadora: calc_real_conservadora(5000, 'EURUSD')\n")
cat("• Scalping agresivo: calc_scalping_agresivo(10000, 'GBPUSD')\n")
cat("• Múltiples escenarios: analisis_escenarios(10000, 'EURUSD')\n")
cat("• Análisis drawdown: calcular_drawdown(10000, 5, 2.0)\n\n")

cat("💡 EJEMPLOS RÁPIDOS:\n")
cat("# Para EUR/USD a 1.16580 con cuenta demo\n")
cat("calc_demo_estandar('EURUSD', 1.16580)\n\n")
cat("# Análisis completo de escenarios\n")
cat("analisis_escenarios(10000, 'GBPUSD', 1.27500)\n\n")

cat("⚠️ RECORDATORIOS:\n")
cat("• Nunca arriesgar más del 2% por operación\n")
cat("• Usar siempre Stop Loss\n")
cat("• Mantener ratio Risk:Reward mínimo 1:2\n")
cat("• Practicar en demo antes de cuenta real\n\n")
