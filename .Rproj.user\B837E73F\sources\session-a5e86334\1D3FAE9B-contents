# =============================================================================
# ESTRATEGIA SCALPING PLAN GRATUITO - ALPHA VANTAGE
# =============================================================================
# Estrategia adaptada para plan gratuito de Alpha Vantage
# Combina datos diarios + monitoreo de precios en tiempo real
# =============================================================================

library(httr)
library(jsonlite)
library(quantmod)
library(TTR)

cat("🎯 ESTRATEGIA SCALPING PLAN GRATUITO\n")
cat("====================================\n")
cat("Datos diarios + Monitoreo tiempo real\n")
cat("Compatible con plan gratuito Alpha Vantage\n\n")

# =============================================================================
# CONFIGURACIÓN ADAPTADA
# =============================================================================

ESTRATEGIA_GRATUITA <- list(
  # API Configuration
  api_key = "KDF6QXHG5UOYNHJK",
  
  # Capital management
  capital_total = 20000,
  riesgo_por_operacion_pct = 0.5,
  reward_risk_ratio = 2.0,
  max_operaciones_dia = 5,
  
  # Pares para monitoreo
  pares = list(
    "EUR/USD" = "EURUSD",
    "GBP/USD" = "GBPUSD",
    "USD/JPY" = "USDJPY",
    "AUD/USD" = "AUDUSD"
  ),
  
  # Indicadores para datos diarios
  ema_rapida = 8,
  ema_lenta = 21,
  rsi_periodo = 14,
  
  # Configuración de monitoreo en tiempo real
  intervalo_monitoreo_segundos = 60,  # Cada minuto
  umbral_movimiento_pct = 0.1,        # 0.1% movimiento significativo
  umbral_alerta_pct = 0.2,            # 0.2% para alerta
  
  # Stop Loss y Take Profit
  stop_loss_pct = 0.5,    # 0.5%
  take_profit_pct = 1.0   # 1.0% (1:2 ratio)
)

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS DIARIOS
# =============================================================================

obtener_datos_diarios <- function(simbolo) {
  cat("📊 Obteniendo datos diarios de", simbolo, "...")
  
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_DAILY",
    "&from_symbol=", substr(simbolo, 1, 3),
    "&to_symbol=", substr(simbolo, 4, 6),
    "&outputsize=compact",
    "&apikey=", ESTRATEGIA_GRATUITA$api_key
  )
  
  tryCatch({
    response <- GET(url, timeout(30))
    
    if (status_code(response) != 200) {
      cat(" ❌ Error HTTP\n")
      return(NULL)
    }
    
    data <- fromJSON(content(response, "text", encoding = "UTF-8"))
    
    if ("Error Message" %in% names(data)) {
      cat(" ❌ Error API\n")
      return(NULL)
    }
    
    if ("Note" %in% names(data)) {
      cat(" ⚠️ Límite API\n")
      return(NULL)
    }
    
    if (!"Time Series FX (Daily)" %in% names(data)) {
      cat(" ❌ Sin datos diarios\n")
      return(NULL)
    }
    
    time_series <- data$`Time Series FX (Daily)`
    
    if (length(time_series) == 0) {
      cat(" ❌ Datos vacíos\n")
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      Date = as.Date(names(time_series)),
      Open = as.numeric(sapply(time_series, function(x) x$`1. open`)),
      High = as.numeric(sapply(time_series, function(x) x$`2. high`)),
      Low = as.numeric(sapply(time_series, function(x) x$`3. low`)),
      Close = as.numeric(sapply(time_series, function(x) x$`4. close`)),
      stringsAsFactors = FALSE
    )
    
    # Ordenar por fecha más reciente primero
    df <- df[order(df$Date, decreasing = TRUE), ]
    
    cat(" ✅", nrow(df), "días\n")
    return(df)
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA OBTENER PRECIO EN TIEMPO REAL
# =============================================================================

obtener_precio_tiempo_real <- function(simbolo) {
  from_currency <- substr(simbolo, 1, 3)
  to_currency <- substr(simbolo, 4, 6)
  
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=CURRENCY_EXCHANGE_RATE",
    "&from_currency=", from_currency,
    "&to_currency=", to_currency,
    "&apikey=", ESTRATEGIA_GRATUITA$api_key
  )
  
  tryCatch({
    response <- GET(url, timeout(15))
    
    if (status_code(response) == 200) {
      data <- fromJSON(content(response, "text", encoding = "UTF-8"))
      
      if ("Realtime Currency Exchange Rate" %in% names(data)) {
        rate_data <- data$`Realtime Currency Exchange Rate`
        
        return(list(
          precio = as.numeric(rate_data$`5. Exchange Rate`),
          bid = as.numeric(rate_data$`8. Bid Price`),
          ask = as.numeric(rate_data$`9. Ask Price`),
          timestamp = rate_data$`6. Last Refreshed`
        ))
      }
    }
    
    return(NULL)
    
  }, error = function(e) {
    return(NULL)
  })
}

# =============================================================================
# ANÁLISIS DE TENDENCIA CON DATOS DIARIOS
# =============================================================================

analizar_tendencia_diaria <- function(datos_diarios) {
  if (is.null(datos_diarios) || nrow(datos_diarios) < 30) {
    return(list(tendencia = "SIN_DATOS", fuerza = 0))
  }
  
  # Ordenar cronológicamente para cálculos
  datos_calc <- datos_diarios[order(datos_diarios$Date), ]
  
  # Calcular indicadores
  datos_calc$EMA_Rapida <- EMA(datos_calc$Close, n = ESTRATEGIA_GRATUITA$ema_rapida)
  datos_calc$EMA_Lenta <- EMA(datos_calc$Close, n = ESTRATEGIA_GRATUITA$ema_lenta)
  datos_calc$RSI <- RSI(datos_calc$Close, n = ESTRATEGIA_GRATUITA$rsi_periodo)
  
  # Volver a ordenar por fecha más reciente
  datos_calc <- datos_calc[order(datos_calc$Date, decreasing = TRUE), ]
  
  # Análisis del último día
  ultimo <- datos_calc[1, ]
  anterior <- datos_calc[2, ]
  
  if (any(is.na(c(ultimo$EMA_Rapida, ultimo$EMA_Lenta, ultimo$RSI)))) {
    return(list(tendencia = "DATOS_INVALIDOS", fuerza = 0))
  }
  
  tendencia <- "NEUTRAL"
  fuerza <- 0
  señales <- c()
  
  # Análisis de tendencia
  if (ultimo$EMA_Rapida > ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida <= anterior$EMA_Lenta) {
      tendencia <- "ALCISTA_FUERTE"
      fuerza <- 3
      señales <- c(señales, "Cruce EMA alcista")
    } else {
      tendencia <- "ALCISTA"
      fuerza <- 2
      señales <- c(señales, "Tendencia EMA alcista")
    }
  } else if (ultimo$EMA_Rapida < ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida >= anterior$EMA_Lenta) {
      tendencia <- "BAJISTA_FUERTE"
      fuerza <- 3
      señales <- c(señales, "Cruce EMA bajista")
    } else {
      tendencia <- "BAJISTA"
      fuerza <- 2
      señales <- c(señales, "Tendencia EMA bajista")
    }
  }
  
  # Análisis RSI
  if (ultimo$RSI < 30) {
    señales <- c(señales, "RSI sobreventa")
    if (tendencia == "BAJISTA") fuerza <- fuerza + 1
  } else if (ultimo$RSI > 70) {
    señales <- c(señales, "RSI sobrecompra")
    if (tendencia == "ALCISTA") fuerza <- fuerza + 1
  }
  
  return(list(
    tendencia = tendencia,
    fuerza = fuerza,
    precio_cierre = ultimo$Close,
    rsi = ultimo$RSI,
    ema_rapida = ultimo$EMA_Rapida,
    ema_lenta = ultimo$EMA_Lenta,
    señales = señales
  ))
}

# =============================================================================
# MONITOREO EN TIEMPO REAL
# =============================================================================

monitoreo_tiempo_real <- function(simbolo, precio_referencia, tendencia_diaria) {
  precio_actual <- obtener_precio_tiempo_real(simbolo)
  
  if (is.null(precio_actual)) {
    return(list(señal = "ERROR", movimiento = 0))
  }
  
  # Calcular movimiento porcentual
  movimiento_pct <- ((precio_actual$precio - precio_referencia) / precio_referencia) * 100
  
  señal <- "NEUTRAL"
  
  # Detectar movimientos significativos
  if (abs(movimiento_pct) >= ESTRATEGIA_GRATUITA$umbral_movimiento_pct) {
    
    # Si la tendencia diaria es alcista y hay movimiento alcista
    if (tendencia_diaria$tendencia %in% c("ALCISTA", "ALCISTA_FUERTE") && 
        movimiento_pct > 0) {
      señal <- "COMPRA_MOMENTUM"
    }
    
    # Si la tendencia diaria es bajista y hay movimiento bajista
    else if (tendencia_diaria$tendencia %in% c("BAJISTA", "BAJISTA_FUERTE") && 
             movimiento_pct < 0) {
      señal <- "VENTA_MOMENTUM"
    }
    
    # Movimiento contrario a la tendencia (posible reversión)
    else if (tendencia_diaria$tendencia %in% c("ALCISTA", "ALCISTA_FUERTE") && 
             movimiento_pct < -ESTRATEGIA_GRATUITA$umbral_alerta_pct) {
      señal <- "ALERTA_REVERSA_BAJISTA"
    }
    
    else if (tendencia_diaria$tendencia %in% c("BAJISTA", "BAJISTA_FUERTE") && 
             movimiento_pct > ESTRATEGIA_GRATUITA$umbral_alerta_pct) {
      señal <- "ALERTA_REVERSA_ALCISTA"
    }
  }
  
  return(list(
    señal = señal,
    precio_actual = precio_actual$precio,
    movimiento_pct = movimiento_pct,
    bid = precio_actual$bid,
    ask = precio_actual$ask,
    timestamp = precio_actual$timestamp
  ))
}

# =============================================================================
# FUNCIÓN PARA GENERAR GUÍA DE TRADING XTB
# =============================================================================

generar_guia_xtb <- function(resultados) {
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("🎯 GUÍA PASO A PASO PARA XTB\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  # Filtrar y priorizar oportunidades
  oportunidades_trading <- list()

  for (par in names(resultados)) {
    res <- resultados[[par]]

    # Solo incluir señales de momentum claras
    if (res$tiempo_real$señal %in% c("COMPRA_MOMENTUM", "VENTA_MOMENTUM")) {
      oportunidades_trading[[par]] <- res
    }
  }

  if (length(oportunidades_trading) == 0) {
    cat("⚠️ No hay señales de momentum claras para operar ahora\n")
    cat("💡 Espera señales de COMPRA_MOMENTUM o VENTA_MOMENTUM\n")
    return()
  }

  # Ordenar por fuerza de tendencia
  oportunidades_ordenadas <- oportunidades_trading[order(
    sapply(oportunidades_trading, function(x) x$tendencia$fuerza),
    decreasing = TRUE
  )]

  cat("🚀 OPERACIONES RECOMENDADAS:", length(oportunidades_ordenadas), "\n\n")

  operacion_num <- 1

  for (par in names(oportunidades_ordenadas)) {
    res <- oportunidades_ordenadas[[par]]
    precio_actual <- res$tiempo_real$precio_actual

    cat("", paste(rep("=", 50), collapse = ""), "\n")
    cat("🎯 OPERACIÓN", operacion_num, ":", par, "\n")
    cat(paste(rep("=", 50), collapse = ""), "\n")

    # Determinar tipo de operación
    if (res$tiempo_real$señal == "COMPRA_MOMENTUM") {
      tipo_operacion <- "COMPRAR"
      color_boton <- "VERDE"
      direccion <- "alcista"

      # Calcular niveles para compra
      stop_loss <- precio_actual - 0.0050  # 50 pips abajo
      take_profit <- precio_actual + 0.0100  # 100 pips arriba

    } else {  # VENTA_MOMENTUM
      tipo_operacion <- "VENDER"
      color_boton <- "ROJO"
      direccion <- "bajista"

      # Calcular niveles para venta
      stop_loss <- precio_actual + 0.0050  # 50 pips arriba
      take_profit <- precio_actual - 0.0100  # 100 pips abajo
    }

    cat("📊 ANÁLISIS:\n")
    cat("   • Tendencia:", res$tendencia$tendencia, "(Fuerza:", res$tendencia$fuerza, ")\n")
    cat("   • Momentum:", direccion, sprintf("(%+.3f%%)", res$tiempo_real$movimiento_pct), "\n")
    cat("   • Precio actual:", sprintf("%.5f", precio_actual), "\n")
    cat("   • Acción:", tipo_operacion, par, "\n\n")

    cat("🎯 PASOS EN XTB:\n")
    cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
    cat("1️⃣ BUSCAR PAR:\n")
    cat("   • En XTB busca:", par, "\n")
    cat("   • Selecciona el par de divisas\n\n")

    cat("2️⃣ ABRIR OPERACIÓN:\n")
    cat("   • Clic en botón", color_boton, ":", tipo_operacion, "\n")
    cat("   • Verificar que dice", tipo_operacion, par, "\n\n")

    cat("3️⃣ CONFIGURAR VOLUMEN:\n")
    cat("   • Volumen: 0.10 lotes\n")
    cat("   • (Para cuenta demo $10,000)\n\n")

    cat("4️⃣ CONFIGURAR STOP LOSS:\n")
    cat("   • Stop Loss:", sprintf("%.5f", stop_loss), "\n")
    cat("   • (50 pips de protección)\n\n")

    cat("5️⃣ CONFIGURAR TAKE PROFIT:\n")
    cat("   • Take Profit:", sprintf("%.5f", take_profit), "\n")
    cat("   • (100 pips de ganancia)\n\n")

    cat("6️⃣ VERIFICAR Y EJECUTAR:\n")
    cat("   • Revisar todos los valores\n")
    cat("   • Clic en 'ABRIR OPERACIÓN'\n")
    cat("   • ✅ Operación ejecutada\n\n")

    cat("💰 GESTIÓN DE RIESGO:\n")
    cat("   • Riesgo máximo: $100 (1% de $10,000)\n")
    cat("   • Ganancia potencial: $200 (2% de $10,000)\n")
    cat("   • Ratio Risk:Reward = 1:2 ✅\n\n")

    cat("📱 MONITOREO:\n")
    cat("   • Abrir gráfico de", par, "en timeframe 15min\n")
    cat("   • Verificar que el movimiento continúe", direccion, "\n")
    cat("   • Si hay reversión fuerte, considerar cierre manual\n\n")

    operacion_num <- operacion_num + 1

    # Máximo 2 operaciones para gestión de riesgo
    if (operacion_num > 2) {
      cat("⚠️ LÍMITE DE OPERACIONES ALCANZADO\n")
      cat("💡 Máximo 2 operaciones simultáneas para gestión de riesgo\n")
      break
    }
  }

  # Resumen final de configuración
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("📋 RESUMEN DE CONFIGURACIÓN XTB\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  cat("💰 CONFIGURACIÓN GENERAL:\n")
  cat("   • Cuenta: DEMO (verificar que sea demo)\n")
  cat("   • Capital: $10,000 (típico demo)\n")
  cat("   • Volumen por operación: 0.10 lotes\n")
  cat("   • Máximo operaciones: 2 simultáneas\n\n")

  cat("📊 NIVELES ESTÁNDAR:\n")
  cat("   • Stop Loss: 50 pips\n")
  cat("   • Take Profit: 100 pips\n")
  cat("   • Ratio: 1:2 (Risk:Reward)\n\n")

  cat("⚠️ VERIFICACIONES IMPORTANTES:\n")
  cat("   • ✅ Confirmar que es cuenta DEMO\n")
  cat("   • ✅ Verificar dirección (COMPRAR/VENDER)\n")
  cat("   • ✅ Confirmar volumen 0.10 lotes\n")
  cat("   • ✅ Verificar Stop Loss y Take Profit\n")
  cat("   • ✅ Máximo 2 operaciones abiertas\n\n")

  cat("🚨 ALERTAS DE SEGURIDAD:\n")
  cat("   • NO operar con dinero real hasta dominar la estrategia\n")
  cat("   • Practicar en demo al menos 1 mes\n")
  cat("   • Nunca arriesgar más del 2% por operación\n")
  cat("   • Cerrar operaciones si hay noticias importantes\n\n")

  cat("📞 SOPORTE:\n")
  cat("   • Si tienes dudas, practica primero en demo\n")
  cat("   • Revisa tutoriales de XTB para familiarizarte\n")
  cat("   • Empieza con 1 operación hasta ganar confianza\n\n")
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ANÁLISIS
# =============================================================================

analisis_completo_gratuito <- function() {
  cat("🎯 ANÁLISIS COMPLETO - PLAN GRATUITO\n")
  cat("====================================\n")
  cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")
  
  resultados <- list()
  
  for (i in 1:length(ESTRATEGIA_GRATUITA$pares)) {
    nombre_par <- names(ESTRATEGIA_GRATUITA$pares)[i]
    simbolo <- ESTRATEGIA_GRATUITA$pares[[i]]
    
    cat("📊", nombre_par, "\n")
    cat(paste(rep("-", 25), collapse = ""), "\n")
    
    # 1. Obtener datos diarios
    datos_diarios <- obtener_datos_diarios(simbolo)
    
    if (is.null(datos_diarios)) {
      cat("❌ Sin datos diarios\n\n")
      next
    }
    
    # 2. Analizar tendencia diaria
    tendencia <- analizar_tendencia_diaria(datos_diarios)
    
    cat("📈 Tendencia diaria:", tendencia$tendencia, "\n")
    cat("💪 Fuerza:", tendencia$fuerza, "\n")
    cat("💰 Precio cierre:", sprintf("%.5f", tendencia$precio_cierre), "\n")
    cat("📊 RSI:", sprintf("%.2f", tendencia$rsi), "\n")
    
    if (length(tendencia$señales) > 0) {
      cat("📋 Señales:\n")
      for (señal in tendencia$señales) {
        cat("   •", señal, "\n")
      }
    }
    
    # 3. Monitoreo en tiempo real
    cat("⏱️ Monitoreando precio actual...\n")
    tiempo_real <- monitoreo_tiempo_real(simbolo, tendencia$precio_cierre, tendencia)
    
    if (tiempo_real$señal != "ERROR") {
      cat("💰 Precio actual:", sprintf("%.5f", tiempo_real$precio_actual), "\n")
      cat("📊 Movimiento:", sprintf("%+.3f%%", tiempo_real$movimiento_pct), "\n")
      cat("🔍 Señal tiempo real:", tiempo_real$señal, "\n")
      
      # Guardar resultados interesantes
      if (tiempo_real$señal != "NEUTRAL" || tendencia$fuerza >= 2) {
        resultados[[nombre_par]] <- list(
          tendencia = tendencia,
          tiempo_real = tiempo_real
        )
        cat("⭐ OPORTUNIDAD DETECTADA!\n")
      }
    }
    
    cat("\n")
    
    # Pausa para respetar límites de API
    if (i < length(ESTRATEGIA_GRATUITA$pares)) {
      cat("⏳ Pausa API (15s)...\n")
      Sys.sleep(15)
    }
  }
  
  # Resumen final
  cat(paste(rep("=", 50), collapse = ""), "\n")
  cat("📊 RESUMEN DE OPORTUNIDADES\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")

  if (length(resultados) == 0) {
    cat("😴 No hay oportunidades claras en este momento\n")
    cat("💡 Recomendación: Revisar en 30-60 minutos\n")
  } else {
    cat("🚨 OPORTUNIDADES DETECTADAS:", length(resultados), "\n\n")

    for (par in names(resultados)) {
      res <- resultados[[par]]
      cat("🎯", par, "\n")
      cat("   Tendencia:", res$tendencia$tendencia, "(Fuerza:", res$tendencia$fuerza, ")\n")
      cat("   Señal tiempo real:", res$tiempo_real$señal, "\n")
      cat("   Movimiento:", sprintf("%+.3f%%", res$tiempo_real$movimiento_pct), "\n\n")
    }

    # Generar guía de trading para XTB
    generar_guia_xtb(resultados)
  }

  cat("⏰ Próxima revisión recomendada en 30-60 minutos\n")
  cat("🔄 Para monitoreo continuo de precios, usar función específica\n")
  
  return(resultados)
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Análisis completo: analisis_completo_gratuito()\n")
cat("• Solo tendencia diaria: analizar_tendencia_diaria(datos)\n")
cat("• Solo precio actual: obtener_precio_tiempo_real('EURUSD')\n\n")

cat("💡 VENTAJAS DEL PLAN GRATUITO:\n")
cat("• Análisis de tendencia confiable con datos diarios\n")
cat("• Monitoreo de precios en tiempo real\n")
cat("• Detección de movimientos significativos\n")
cat("• Combinación de análisis técnico + momentum\n\n")

cat("🎯 ESTRATEGIA RECOMENDADA:\n")
cat("1. Ejecutar análisis completo 2-3 veces al día\n")
cat("2. Identificar tendencia diaria dominante\n")
cat("3. Monitorear movimientos en tiempo real\n")
cat("4. Operar a favor de la tendencia diaria\n\n")

cat("🚀 EJECUTAR AHORA:\n")
cat("oportunidades <- analisis_completo_gratuito()\n\n")

# =============================================================================
# FUNCIÓN PARA MONITOREO RÁPIDO
# =============================================================================

monitoreo_rapido_xtb <- function() {
  cat("⚡ MONITOREO RÁPIDO PARA XTB\n")
  cat("============================\n")
  cat("Análisis express de oportunidades\n\n")

  # Ejecutar análisis completo
  resultados <- analisis_completo_gratuito()

  # Si hay oportunidades, mostrar resumen ejecutivo
  if (length(resultados) > 0) {
    cat("\n🎯 RESUMEN EJECUTIVO PARA XTB:\n")
    cat("==============================\n")

    for (par in names(resultados)) {
      res <- resultados[[par]]

      if (res$tiempo_real$señal %in% c("COMPRA_MOMENTUM", "VENTA_MOMENTUM")) {
        precio <- res$tiempo_real$precio_actual

        if (res$tiempo_real$señal == "COMPRA_MOMENTUM") {
          cat("🟢 COMPRAR", par, "a", sprintf("%.5f", precio), "\n")
          cat("   SL:", sprintf("%.5f", precio - 0.0050), "| TP:", sprintf("%.5f", precio + 0.0100), "\n")
        } else {
          cat("🔴 VENDER", par, "a", sprintf("%.5f", precio), "\n")
          cat("   SL:", sprintf("%.5f", precio + 0.0050), "| TP:", sprintf("%.5f", precio - 0.0100), "\n")
        }
        cat("   Volumen: 0.10 lotes | Fuerza:", res$tendencia$fuerza, "\n\n")
      }
    }
  }

  return(resultados)
}

cat("⚡ FUNCIÓN ADICIONAL:\n")
cat("• Monitoreo rápido: monitoreo_rapido_xtb()\n")
cat("• Muestra solo lo esencial para XTB\n\n")
