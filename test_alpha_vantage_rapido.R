# =============================================================================
# PRUEBA RÁPIDA ALPHA VANTAGE - VERIFICACIÓN INMEDIATA
# =============================================================================
# Script para probar rápidamente la conexión y obtener datos
# Ejecutar después de la configuración inicial
# =============================================================================

library(httr)
library(jsonlite)

cat("🧪 PRUEBA RÁPIDA ALPHA VANTAGE\n")
cat("==============================\n")
cat("Verificando conexión y datos en tiempo real...\n\n")

# =============================================================================
# CONFIGURACIÓN DE PRUEBA
# =============================================================================

# Intentar cargar API key desde archivo
api_key <- "DEMO"
if (file.exists("alpha_vantage_config.txt")) {
  tryCatch({
    api_key <- readLines("alpha_vantage_config.txt", warn = FALSE)[1]
    cat("✅ API key cargada desde archivo\n")
  }, error = function(e) {
    cat("⚠️ No se pudo cargar API key, usando DEMO\n")
  })
} else {
  cat("⚠️ Archivo de configuración no encontrado, usando DEMO\n")
}

cat("🔑 API Key:", substr(api_key, 1, 8), "...\n\n")

# =============================================================================
# FUNCIÓN DE PRUEBA SIMPLE
# =============================================================================

probar_datos_forex <- function(par = "EURUSD", timeframe = "1min") {
  cat("📡 Probando", par, "en timeframe", timeframe, "...\n")
  
  # Construir URL
  from_symbol <- substr(par, 1, 3)
  to_symbol <- substr(par, 4, 6)
  
  url <- paste0(
    "https://www.alphavantage.co/query",
    "?function=FX_INTRADAY",
    "&from_symbol=", from_symbol,
    "&to_symbol=", to_symbol,
    "&interval=", timeframe,
    "&outputsize=compact",
    "&apikey=", api_key
  )
  
  cat("🌐 URL:", substr(url, 1, 80), "...\n")
  
  tryCatch({
    # Realizar petición
    start_time <- Sys.time()
    response <- GET(url, timeout(30))
    end_time <- Sys.time()
    
    tiempo_respuesta <- as.numeric(end_time - start_time)
    cat("⏱️ Tiempo de respuesta:", round(tiempo_respuesta, 2), "segundos\n")
    
    # Verificar status HTTP
    if (status_code(response) != 200) {
      cat("❌ Error HTTP:", status_code(response), "\n")
      return(NULL)
    }
    
    # Parsear JSON
    data <- fromJSON(content(response, "text"))
    
    # Verificar errores
    if ("Error Message" %in% names(data)) {
      cat("❌ Error API:", data$`Error Message`, "\n")
      return(NULL)
    }
    
    if ("Note" %in% names(data)) {
      cat("⚠️ Límite API:", data$Note, "\n")
      return(NULL)
    }
    
    # Verificar datos
    time_series_key <- paste0("Time Series FX (", timeframe, ")")
    
    if (!time_series_key %in% names(data)) {
      cat("❌ No se encontraron datos de series temporales\n")
      cat("📋 Claves disponibles:", paste(names(data), collapse = ", "), "\n")
      return(NULL)
    }
    
    time_series <- data[[time_series_key]]
    
    if (length(time_series) == 0) {
      cat("❌ Series temporales vacías\n")
      return(NULL)
    }
    
    # Mostrar información de los datos
    cat("✅ Datos obtenidos exitosamente!\n")
    cat("📊 Número de registros:", length(time_series), "\n")
    
    # Mostrar últimos 3 precios
    cat("\n💰 ÚLTIMOS PRECIOS:\n")
    timestamps <- names(time_series)[1:min(3, length(time_series))]
    
    for (i in 1:length(timestamps)) {
      timestamp <- timestamps[i]
      precio_data <- time_series[[timestamp]]
      
      cat(sprintf("   %s: Open=%.5f High=%.5f Low=%.5f Close=%.5f\n",
                  timestamp,
                  as.numeric(precio_data$`1. open`),
                  as.numeric(precio_data$`2. high`),
                  as.numeric(precio_data$`3. low`),
                  as.numeric(precio_data$`4. close`)))
    }
    
    # Información de metadata
    if ("Meta Data" %in% names(data)) {
      meta <- data$`Meta Data`
      cat("\n📋 METADATA:\n")
      cat("   Información:", meta$`1. Information`, "\n")
      cat("   Par:", meta$`2. From Symbol`, "/", meta$`3. To Symbol`, "\n")
      cat("   Última actualización:", meta$`4. Last Refreshed`, "\n")
      cat("   Intervalo:", meta$`5. Interval`, "\n")
      cat("   Zona horaria:", meta$`6. Output Size`, "\n")
    }
    
    return(time_series)
    
  }, error = function(e) {
    cat("❌ Error en la petición:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# EJECUTAR PRUEBAS
# =============================================================================

cat("🎯 INICIANDO PRUEBAS...\n")
cat(paste(rep("-", 40), collapse = ""), "\n")

# Prueba 1: EUR/USD 1min
cat("\n1️⃣ PRUEBA EUR/USD 1min:\n")
datos_eur_1min <- probar_datos_forex("EURUSD", "1min")

# Pausa para respetar límites
if (!is.null(datos_eur_1min)) {
  cat("\n⏳ Esperando 12 segundos (límite API)...\n")
  Sys.sleep(12)
  
  # Prueba 2: GBP/USD 5min
  cat("\n2️⃣ PRUEBA GBP/USD 5min:\n")
  datos_gbp_5min <- probar_datos_forex("GBPUSD", "5min")
}

# =============================================================================
# PRUEBA DE ANÁLISIS BÁSICO
# =============================================================================

if (!is.null(datos_eur_1min)) {
  cat("\n", paste(rep("=", 50), collapse = ""), "\n")
  cat("📈 ANÁLISIS BÁSICO EUR/USD\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")
  
  # Extraer últimos 10 precios de cierre
  timestamps <- names(datos_eur_1min)[1:min(10, length(datos_eur_1min))]
  precios <- numeric(length(timestamps))
  
  for (i in 1:length(timestamps)) {
    precios[i] <- as.numeric(datos_eur_1min[[timestamps[i]]]$`4. close`)
  }
  
  # Estadísticas básicas
  precio_actual <- precios[1]
  precio_anterior <- precios[2]
  cambio <- precio_actual - precio_anterior
  cambio_pct <- (cambio / precio_anterior) * 100
  
  cat("💰 Precio actual:", sprintf("%.5f", precio_actual), "\n")
  cat("📊 Cambio:", sprintf("%+.5f", cambio), sprintf("(%+.3f%%)", cambio_pct), "\n")
  cat("📈 Máximo (10 períodos):", sprintf("%.5f", max(precios)), "\n")
  cat("📉 Mínimo (10 períodos):", sprintf("%.5f", min(precios)), "\n")
  cat("📊 Promedio (10 períodos):", sprintf("%.5f", mean(precios)), "\n")
  
  # Tendencia simple
  if (cambio > 0) {
    cat("🔼 Tendencia inmediata: ALCISTA\n")
  } else if (cambio < 0) {
    cat("🔽 Tendencia inmediata: BAJISTA\n")
  } else {
    cat("➡️ Tendencia inmediata: LATERAL\n")
  }
  
  # Volatilidad básica
  volatilidad <- sd(precios)
  cat("📊 Volatilidad (10 períodos):", sprintf("%.5f", volatilidad), "\n")
  
  if (volatilidad > 0.001) {
    cat("⚡ Volatilidad: ALTA - Bueno para scalping\n")
  } else if (volatilidad > 0.0005) {
    cat("📊 Volatilidad: MEDIA - Aceptable para scalping\n")
  } else {
    cat("😴 Volatilidad: BAJA - Poco favorable para scalping\n")
  }
}

# =============================================================================
# RESUMEN Y RECOMENDACIONES
# =============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("📋 RESUMEN DE PRUEBAS\n")
cat(paste(rep("=", 60), collapse = ""), "\n")

if (!is.null(datos_eur_1min)) {
  cat("✅ Conexión Alpha Vantage: EXITOSA\n")
  cat("✅ Datos EUR/USD 1min: OBTENIDOS\n")
  
  if (!is.null(datos_gbp_5min)) {
    cat("✅ Datos GBP/USD 5min: OBTENIDOS\n")
  }
  
  cat("\n🎯 SISTEMA LISTO PARA SCALPING\n")
  cat("📝 Próximos pasos:\n")
  cat("   1. source('alpha_vantage_scalping.R')\n")
  cat("   2. monitoreo_scalping_tiempo_real()\n")
  cat("   3. Ejecutar cada 1-5 minutos para monitoreo continuo\n")
  
} else {
  cat("❌ Conexión Alpha Vantage: FALLIDA\n")
  cat("\n🔧 SOLUCIONES:\n")
  cat("   1. Verificar conexión a internet\n")
  cat("   2. Obtener API key válida: https://www.alphavantage.co/support/#api-key\n")
  cat("   3. Ejecutar setup_alpha_vantage.R nuevamente\n")
  cat("   4. Verificar límites de API (5 llamadas/min)\n")
}

cat("\n⚠️ RECORDATORIOS:\n")
cat("• Plan gratuito: 5 llamadas/min, 500/día\n")
cat("• Datos con retraso ~15 minutos\n")
cat("• Para scalping real considerar plan premium\n")
cat("• Monitorear durante horarios de alta volatilidad\n\n")

cat("🕐 Prueba completada:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
