# =============================================================================
# ESTRATEGIA SCALPING CON YAHOO FINANCE - SIN LÍMITES
# =============================================================================
# Sistema completo usando Yahoo Finance (gratis, sin límites de API)
# Perfecto para scalping con guía completa para XTB
# =============================================================================

library(quantmod)
library(TTR)

cat("🚀 ESTRATEGIA SCALPING YAHOO FINANCE\n")
cat("====================================\n")
cat("Sin límites de API • Datos en tiempo real • Guía XTB\n\n")

# =============================================================================
# CONFIGURACIÓN PARA YAHOO FINANCE
# =============================================================================

YAHOO_CONFIG <- list(
  # Pares de divisas (formato Yahoo Finance)
  pares = list(
    "EUR/USD" = "EURUSD=X",
    "GBP/USD" = "GBPUSD=X", 
    "USD/JPY" = "USDJPY=X",
    "AUD/USD" = "AUDUSD=X"
  ),
  
  # Configuración de trading
  volumen_demo = 0.10,
  stop_loss_pips = 50,
  take_profit_pips = 100,
  
  # Indicadores técnicos
  ema_rapida = 8,
  ema_lenta = 21,
  rsi_periodo = 14,
  
  # Umbrales para señales
  rsi_sobrecompra = 70,
  rsi_sobreventa = 30,
  min_fuerza_señal = 2,
  
  # Gestión de capital
  capital_demo = 10000,
  riesgo_por_operacion_pct = 1.0  # 1% por operación
)

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS DE YAHOO FINANCE
# =============================================================================

obtener_datos_yahoo <- function(simbolo, periodo = "1d", dias = 30) {
  cat("📡 Obteniendo datos de", simbolo, "...")
  
  tryCatch({
    # Obtener datos de Yahoo Finance
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = Sys.Date() - dias,
                       to = Sys.Date(),
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      Date = index(datos),
      Open = as.numeric(Op(datos)),
      High = as.numeric(Hi(datos)),
      Low = as.numeric(Lo(datos)),
      Close = as.numeric(Cl(datos)),
      Volume = as.numeric(Vo(datos))
    )
    
    # Remover NAs
    df <- na.omit(df)
    
    if (nrow(df) < 10) {
      cat(" ❌ Datos insuficientes\n")
      return(NULL)
    }
    
    cat(" ✅", nrow(df), "días\n")
    return(df)
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# ANÁLISIS TÉCNICO COMPLETO
# =============================================================================

analizar_tecnico_yahoo <- function(datos) {
  if (is.null(datos) || nrow(datos) < 30) {
    return(list(señal = "SIN_DATOS", fuerza = 0))
  }
  
  # Ordenar por fecha
  datos <- datos[order(datos$Date), ]
  
  # Calcular indicadores
  datos$EMA_Rapida <- EMA(datos$Close, n = YAHOO_CONFIG$ema_rapida)
  datos$EMA_Lenta <- EMA(datos$Close, n = YAHOO_CONFIG$ema_lenta)
  datos$RSI <- RSI(datos$Close, n = YAHOO_CONFIG$rsi_periodo)
  
  # Bandas de Bollinger
  bb <- BBands(datos$Close, n = 20, sd = 2)
  datos$BB_Upper <- bb[, "up"]
  datos$BB_Middle <- bb[, "mavg"]
  datos$BB_Lower <- bb[, "dn"]
  
  # Análisis del último día
  ultimo <- tail(datos, 1)
  anterior <- tail(datos, 2)[1, ]
  
  # Verificar datos válidos
  if (any(is.na(c(ultimo$EMA_Rapida, ultimo$EMA_Lenta, ultimo$RSI)))) {
    return(list(señal = "DATOS_INVALIDOS", fuerza = 0))
  }
  
  señal <- "NEUTRAL"
  fuerza <- 0
  razones <- c()
  
  # Análisis EMA
  if (ultimo$EMA_Rapida > ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida <= anterior$EMA_Lenta) {
      señal <- "COMPRA"
      fuerza <- fuerza + 3
      razones <- c(razones, "Cruce EMA alcista")
    } else {
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA alcista")
    }
  } else if (ultimo$EMA_Rapida < ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida >= anterior$EMA_Lenta) {
      señal <- "VENTA"
      fuerza <- fuerza + 3
      razones <- c(razones, "Cruce EMA bajista")
    } else {
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA bajista")
    }
  }
  
  # Análisis RSI
  if (ultimo$RSI < YAHOO_CONFIG$rsi_sobreventa) {
    if (señal == "NEUTRAL") señal <- "COMPRA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobreventa")
  } else if (ultimo$RSI > YAHOO_CONFIG$rsi_sobrecompra) {
    if (señal == "NEUTRAL") señal <- "VENTA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobrecompra")
  }
  
  # Análisis Bollinger Bands
  if (ultimo$Close <= ultimo$BB_Lower) {
    if (señal == "NEUTRAL") señal <- "COMPRA"
    fuerza <- fuerza + 1
    razones <- c(razones, "Precio en banda inferior")
  } else if (ultimo$Close >= ultimo$BB_Upper) {
    if (señal == "NEUTRAL") señal <- "VENTA"
    fuerza <- fuerza + 1
    razones <- c(razones, "Precio en banda superior")
  }
  
  return(list(
    señal = señal,
    fuerza = fuerza,
    precio = ultimo$Close,
    rsi = ultimo$RSI,
    ema_rapida = ultimo$EMA_Rapida,
    ema_lenta = ultimo$EMA_Lenta,
    fecha = ultimo$Date,
    razones = razones
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ANÁLISIS
# =============================================================================

analisis_yahoo_xtb <- function() {
  cat("🎯 ANÁLISIS SCALPING YAHOO FINANCE\n")
  cat("==================================\n")
  cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")
  
  oportunidades <- list()
  
  for (par_nombre in names(YAHOO_CONFIG$pares)) {
    simbolo <- YAHOO_CONFIG$pares[[par_nombre]]
    
    cat("📊", par_nombre, "\n")
    cat(paste(rep("-", 25), collapse = ""), "\n")
    
    # Obtener datos
    datos <- obtener_datos_yahoo(simbolo)
    
    if (!is.null(datos)) {
      # Analizar técnicamente
      analisis <- analizar_tecnico_yahoo(datos)
      
      cat("💰 Precio actual:", sprintf("%.5f", analisis$precio), "\n")
      cat("🔍 Señal:", analisis$señal, "\n")
      cat("💪 Fuerza:", analisis$fuerza, "\n")
      cat("📊 RSI:", sprintf("%.2f", analisis$rsi), "\n")
      
      if (length(analisis$razones) > 0) {
        cat("📋 Razones:\n")
        for (razon in analisis$razones) {
          cat("   •", razon, "\n")
        }
      }
      
      # Guardar oportunidades fuertes
      if (analisis$fuerza >= YAHOO_CONFIG$min_fuerza_señal && 
          analisis$señal %in% c("COMPRA", "VENTA")) {
        oportunidades[[par_nombre]] <- analisis
        cat("⭐ OPORTUNIDAD DETECTADA!\n")
      }
    }
    
    cat("\n")
  }
  
  # Generar guía XTB si hay oportunidades
  if (length(oportunidades) > 0) {
    generar_guia_yahoo_xtb(oportunidades)
  } else {
    cat("😴 No hay oportunidades fuertes en este momento\n")
    cat("💡 Recomendación: Revisar en 1-2 horas\n")
  }
  
  return(oportunidades)
}

# =============================================================================
# GUÍA COMPLETA PARA XTB
# =============================================================================

generar_guia_yahoo_xtb <- function(oportunidades) {
  cat(paste(rep("=", 60), collapse = ""), "\n")
  cat("🎯 GUÍA COMPLETA PARA XTB\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  # Ordenar por fuerza
  oportunidades_ordenadas <- oportunidades[order(
    sapply(oportunidades, function(x) x$fuerza), 
    decreasing = TRUE
  )]
  
  cat("🚨 OPORTUNIDADES DETECTADAS:", length(oportunidades_ordenadas), "\n\n")
  
  operacion_num <- 1
  
  for (par in names(oportunidades_ordenadas)) {
    opp <- oportunidades_ordenadas[[par]]
    precio <- opp$precio
    
    cat("🎯 OPERACIÓN", operacion_num, ":", par, "\n")
    cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
    
    # Determinar tipo de operación y niveles
    if (opp$señal == "COMPRA") {
      tipo <- "COMPRAR"
      color <- "VERDE"
      
      # Calcular niveles para compra
      if (grepl("JPY", par)) {
        # Para pares con JPY (pip = 0.01)
        sl <- precio - (YAHOO_CONFIG$stop_loss_pips * 0.01)
        tp <- precio + (YAHOO_CONFIG$take_profit_pips * 0.01)
      } else {
        # Para otros pares (pip = 0.0001)
        sl <- precio - (YAHOO_CONFIG$stop_loss_pips * 0.0001)
        tp <- precio + (YAHOO_CONFIG$take_profit_pips * 0.0001)
      }
      
    } else {  # VENTA
      tipo <- "VENDER"
      color <- "ROJO"
      
      # Calcular niveles para venta
      if (grepl("JPY", par)) {
        sl <- precio + (YAHOO_CONFIG$stop_loss_pips * 0.01)
        tp <- precio - (YAHOO_CONFIG$take_profit_pips * 0.01)
      } else {
        sl <- precio + (YAHOO_CONFIG$stop_loss_pips * 0.0001)
        tp <- precio - (YAHOO_CONFIG$take_profit_pips * 0.0001)
      }
    }
    
    cat("📊 ANÁLISIS:\n")
    cat("   • Señal:", opp$señal, "(Fuerza:", opp$fuerza, ")\n")
    cat("   • Precio actual:", sprintf("%.5f", precio), "\n")
    cat("   • RSI:", sprintf("%.2f", opp$rsi), "\n")
    cat("   • Razones:", paste(opp$razones, collapse = ", "), "\n\n")
    
    cat("🎯 PASOS EN XTB:\n")
    cat("1️⃣ BUSCAR PAR:\n")
    cat("   • En XTB busca:", par, "\n")
    cat("   • Selecciona el par de divisas\n\n")
    
    cat("2️⃣ ABRIR OPERACIÓN:\n")
    cat("   • Clic en botón", color, ":", tipo, "\n")
    cat("   • Verificar que dice", tipo, par, "\n\n")
    
    cat("3️⃣ CONFIGURAR:\n")
    cat("   • Volumen:", YAHOO_CONFIG$volumen_demo, "lotes\n")
    cat("   • Stop Loss:", sprintf("%.5f", sl), "\n")
    cat("   • Take Profit:", sprintf("%.5f", tp), "\n\n")
    
    cat("4️⃣ EJECUTAR:\n")
    cat("   • Revisar todos los valores\n")
    cat("   • Clic en 'ABRIR OPERACIÓN'\n")
    cat("   • ✅ Operación ejecutada\n\n")
    
    cat("💰 GESTIÓN:\n")
    cat("   • Riesgo máximo: $100 (1% de $10,000)\n")
    cat("   • Ganancia potencial: $200 (2% de $10,000)\n")
    cat("   • Ratio Risk:Reward = 1:2 ✅\n\n")
    
    operacion_num <- operacion_num + 1
    
    if (operacion_num > 2) {
      cat("⚠️ LÍMITE: Máximo 2 operaciones simultáneas\n")
      break
    }
  }
  
  cat("📋 VERIFICACIONES FINALES:\n")
  cat("   ✅ Confirmar cuenta DEMO\n")
  cat("   ✅ Verificar dirección (COMPRAR/VENDER)\n")
  cat("   ✅ Confirmar volumen y niveles\n")
  cat("   ✅ Máximo 2 operaciones abiertas\n\n")
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES:\n")
cat("==================\n")
cat("• Análisis completo: analisis_yahoo_xtb()\n")
cat("• Sin límites de API\n")
cat("• Datos actualizados de Yahoo Finance\n")
cat("• Guía paso a paso para XTB\n\n")

cat("🚀 VENTAJAS YAHOO FINANCE:\n")
cat("• ✅ Completamente GRATIS\n")
cat("• ✅ Sin límites diarios\n")
cat("• ✅ Datos en tiempo real\n")
cat("• ✅ Muy confiable\n")
cat("• ✅ Perfecto para scalping\n\n")

cat("🎯 EJECUTAR AHORA:\n")
cat("oportunidades <- analisis_yahoo_xtb()\n\n")

cat("💡 RECOMENDACIÓN:\n")
cat("• Ejecutar cada 2-4 horas\n")
cat("• Combinar con análisis de gráficos\n")
cat("• Practicar en demo antes de real\n\n")
