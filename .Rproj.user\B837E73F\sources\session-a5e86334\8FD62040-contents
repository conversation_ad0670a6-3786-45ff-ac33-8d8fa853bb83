# =============================================================================
# DAY TRADING MONITOR - ANÁLISIS EN TIEMPO REAL
# =============================================================================
# Monitor para detectar señales de day trading durante el día
# Ejecutar cada 15-30 minutos durante horario de trading
# =============================================================================

library(quantmod)
library(TTR)

cat("📱 DAY TRADING MONITOR\n")
cat("======================\n")
cat("Monitor de señales intraday\n\n")

# =============================================================================
# CONFIGURACIÓN DEL MONITOR
# =============================================================================

CONFIG_MONITOR <- list(
  # Gestión de capital
  capital_total = 10000,              # Ajustar a tu capital real
  capital_utilizable_pct = 0.10,      # 10%
  riesgo_por_operacion_pct = 0.02,    # 2%
  stop_loss_pct = 0.20,               # 20%
  take_profit_pct = 0.60,             # 60%
  max_operaciones_dia = 3,            # Máximo 3 por día
  
  # Pares a monitorear
  pares = list(
    "EUR/USD" = "EURUSD=X",
    "GBP/USD" = "GBPUSD=X",
    "USD/JPY" = "USDJPY=X",
    "AUD/USD" = "AUDUSD=X"
  ),
  
  # Indicadores
  ma_rapida = 8,
  ma_lenta = 21,
  rsi_periodo = 14,
  rsi_min = 45,
  rsi_max = 70
)

# =============================================================================
# FUNCIÓN PARA CALCULAR TAMAÑO DE POSICIÓN
# =============================================================================

calcular_posicion <- function(precio_entrada, stop_loss_pct, capital_total, riesgo_pct) {
  capital_utilizable <- capital_total * CONFIG_MONITOR$capital_utilizable_pct
  riesgo_euros <- capital_total * riesgo_pct
  
  # Calcular stop loss en precio
  stop_loss_precio <- precio_entrada * (1 - stop_loss_pct)
  riesgo_por_pip <- precio_entrada - stop_loss_precio
  
  # Tamaño de posición basado en riesgo
  if (riesgo_por_pip > 0) {
    tamaño_lotes <- riesgo_euros / (riesgo_por_pip * 100000)  # Para forex
    tamaño_lotes <- round(tamaño_lotes, 2)
  } else {
    tamaño_lotes <- 0.01  # Mínimo
  }
  
  return(list(
    tamaño_lotes = tamaño_lotes,
    capital_utilizable = capital_utilizable,
    riesgo_euros = riesgo_euros,
    stop_loss_precio = stop_loss_precio,
    take_profit_precio = precio_entrada * (1 + CONFIG_MONITOR$take_profit_pct)
  ))
}

# =============================================================================
# FUNCIÓN PARA ANALIZAR UN PAR EN TIEMPO REAL
# =============================================================================

analizar_par_tiempo_real <- function(nombre_par, simbolo) {
  tryCatch({
    # Obtener datos recientes
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 7  # 1 semana
    
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 50) {
      return(list(par = nombre_par, señal = "Sin datos", prioridad = 0))
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores
    precios <- Cl(datos)
    ma_rapida <- SMA(precios, n = CONFIG_MONITOR$ma_rapida)
    ma_lenta <- SMA(precios, n = CONFIG_MONITOR$ma_lenta)
    rsi <- RSI(precios, n = CONFIG_MONITOR$rsi_periodo)
    
    # Datos actuales
    n <- nrow(datos)
    if (n < 2) return(list(par = nombre_par, señal = "Datos insuficientes", prioridad = 0))
    
    precio_actual <- as.numeric(precios[n])
    ma_rapida_actual <- as.numeric(ma_rapida[n])
    ma_lenta_actual <- as.numeric(ma_lenta[n])
    ma_rapida_anterior <- as.numeric(ma_rapida[n-1])
    ma_lenta_anterior <- as.numeric(ma_lenta[n-1])
    rsi_actual <- as.numeric(rsi[n])
    
    # Verificar señales
    cruce_alcista <- (ma_rapida_anterior <= ma_lenta_anterior) && 
                     (ma_rapida_actual > ma_lenta_actual)
    
    rsi_favorable <- rsi_actual >= CONFIG_MONITOR$rsi_min && 
                     rsi_actual <= CONFIG_MONITOR$rsi_max
    
    tendencia_alcista <- ma_rapida_actual > ma_lenta_actual
    precio_arriba_ma <- precio_actual > ma_lenta_actual
    
    # Determinar señal
    if (cruce_alcista && rsi_favorable && precio_arriba_ma) {
      señal <- "🚀 COMPRAR AHORA"
      prioridad <- 5
    } else if (tendencia_alcista && rsi_favorable && precio_arriba_ma) {
      señal <- "📈 Tendencia alcista"
      prioridad <- 3
    } else if (cruce_alcista) {
      señal <- "⚠️ Cruce (RSI malo)"
      prioridad <- 2
    } else {
      señal <- "⏸️ Esperar"
      prioridad <- 1
    }
    
    # Calcular niveles si hay señal de compra
    niveles <- NULL
    if (prioridad >= 4) {
      niveles <- calcular_posicion(precio_actual, 
                                  CONFIG_MONITOR$stop_loss_pct,
                                  CONFIG_MONITOR$capital_total,
                                  CONFIG_MONITOR$riesgo_por_operacion_pct)
    }
    
    return(list(
      par = nombre_par,
      precio = precio_actual,
      ma_rapida = ma_rapida_actual,
      ma_lenta = ma_lenta_actual,
      rsi = rsi_actual,
      señal = señal,
      prioridad = prioridad,
      niveles = niveles,
      cruce = cruce_alcista,
      tendencia = ifelse(tendencia_alcista, "Alcista", "Bajista")
    ))
    
  }, error = function(e) {
    return(list(par = nombre_par, señal = paste("Error:", e$message), prioridad = 0))
  })
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE MONITOREO
# =============================================================================

monitoreo_day_trading <- function() {
  cat("🔍 ESCANEANDO OPORTUNIDADES DE DAY TRADING\n")
  cat("==========================================\n")
  cat("Hora actual:", format(Sys.time(), "%H:%M:%S"), "\n")
  cat("Fecha:", format(Sys.Date(), "%Y-%m-%d"), "\n\n")
  
  resultados <- list()
  señales_compra <- 0
  
  # Analizar cada par
  for (i in 1:length(CONFIG_MONITOR$pares)) {
    nombre_par <- names(CONFIG_MONITOR$pares)[i]
    simbolo <- CONFIG_MONITOR$pares[[i]]
    
    cat("Analizando", nombre_par, "...")
    resultado <- analizar_par_tiempo_real(nombre_par, simbolo)
    resultados[[i]] <- resultado
    
    if (resultado$prioridad >= 4) {
      señales_compra <- señales_compra + 1
    }
    
    cat(" ✓\n")
    Sys.sleep(0.3)  # Pausa para no sobrecargar
  }
  
  # Ordenar por prioridad
  resultados <- resultados[order(sapply(resultados, function(x) x$prioridad), decreasing = TRUE)]
  
  cat("\n📊 RESUMEN DEL ESCANEO\n")
  cat("======================\n")
  cat("🚀 Señales de COMPRA:", señales_compra, "\n")
  cat("📈 Pares analizados:", length(CONFIG_MONITOR$pares), "\n\n")
  
  # Mostrar oportunidades
  if (señales_compra > 0) {
    cat("🏆 OPORTUNIDADES DETECTADAS:\n")
    cat("============================\n")
    
    for (resultado in resultados) {
      if (resultado$prioridad >= 4) {
        cat("📈", resultado$par, "-", resultado$señal, "\n")
        cat("   Precio:", round(resultado$precio, 5), "\n")
        rsi_texto <- if (!is.null(resultado$rsi) && !is.na(resultado$rsi)) {
          round(resultado$rsi, 2)
        } else {
          "N/A"
        }
        cat("   RSI:", rsi_texto, "\n")
        cat("   Tendencia:", resultado$tendencia, "\n")
        
        if (!is.null(resultado$niveles)) {
          cat("   💰 NIVELES PARA XTB:\n")
          cat("      Tamaño:", resultado$niveles$tamaño_lotes, "lotes\n")
          cat("      Stop Loss:", round(resultado$niveles$stop_loss_precio, 5), "\n")
          cat("      Take Profit:", round(resultado$niveles$take_profit_precio, 5), "\n")
          cat("      Riesgo:", round(resultado$niveles$riesgo_euros, 2), "€\n")
        }
        cat("\n")
      }
    }
    
    cat("📋 CHECKLIST PARA OPERAR:\n")
    cat("□ Verificar que no has hecho 3 operaciones hoy\n")
    cat("□ Confirmar señal en gráfico de 15 minutos en XTB\n")
    cat("□ Verificar que RSI está entre 45-70\n")
    cat("□ Confirmar cruce de medias móviles\n")
    cat("□ Ejecutar con niveles calculados\n")
    cat("□ Anotar operación en registro\n")
    
  } else {
    cat("⏸️ NO HAY SEÑALES DE COMPRA CLARAS\n")
    cat("📊 Estado de los pares:\n\n")
    
    for (resultado in resultados) {
      rsi_texto <- if (!is.null(resultado$rsi) && !is.na(resultado$rsi)) {
        round(resultado$rsi, 2)
      } else {
        "N/A"
      }

      cat("•", resultado$par, ":", resultado$señal,
          "| RSI:", rsi_texto,
          "| Tendencia:", resultado$tendencia, "\n")
    }
    
    cat("\n💡 RECOMENDACIONES:\n")
    cat("- Esperar 15-30 minutos y volver a escanear\n")
    cat("- Vigilar pares con tendencia alcista\n")
    cat("- Revisar noticias económicas importantes\n")
  }
  
  cat("\n⏰ PRÓXIMO ESCANEO: En 15-30 minutos\n")
  cat("📱 Recordatorio: Máximo 3 operaciones por día\n")
}

# =============================================================================
# FUNCIÓN PARA REGISTRO DE OPERACIONES
# =============================================================================

registrar_operacion <- function(par, tipo, precio_entrada, stop_loss, take_profit, tamaño) {
  cat("\n📝 REGISTRANDO OPERACIÓN\n")
  cat("========================\n")
  cat("Par:", par, "\n")
  cat("Tipo:", tipo, "\n")
  cat("Precio entrada:", precio_entrada, "\n")
  cat("Stop Loss:", stop_loss, "\n")
  cat("Take Profit:", take_profit, "\n")
  cat("Tamaño:", tamaño, "lotes\n")
  cat("Hora:", format(Sys.time(), "%H:%M:%S"), "\n")
  
  # Aquí podrías guardar en un archivo CSV
  # write.csv(operacion, "operaciones_day_trading.csv", append = TRUE)
  
  cat("✅ Operación registrada\n")
}

# =============================================================================
# FUNCIÓN PARA VERIFICAR LÍMITES DIARIOS
# =============================================================================

verificar_limites_diarios <- function() {
  cat("🔍 VERIFICANDO LÍMITES DIARIOS\n")
  cat("===============================\n")
  
  # En implementación real, leerías de un archivo de registro
  operaciones_hoy <- 0  # Placeholder
  ganancia_hoy <- 0     # Placeholder
  
  cat("Operaciones realizadas hoy:", operaciones_hoy, "/", CONFIG_MONITOR$max_operaciones_dia, "\n")
  cat("Ganancia/Pérdida del día:", ganancia_hoy, "€\n")
  
  if (operaciones_hoy >= CONFIG_MONITOR$max_operaciones_dia) {
    cat("🛑 LÍMITE ALCANZADO: No más operaciones hoy\n")
    return(FALSE)
  }
  
  target_diario <- CONFIG_MONITOR$capital_total * CONFIG_MONITOR$riesgo_por_operacion_pct * 1.5
  if (ganancia_hoy >= target_diario) {
    cat("🎯 TARGET ALCANZADO: Considera parar por hoy\n")
    return(FALSE)
  }
  
  cat("✅ Puedes continuar operando\n")
  return(TRUE)
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

cat("🎯 Iniciando monitoreo de day trading...\n\n")

# Verificar límites
puede_operar <- verificar_limites_diarios()

if (puede_operar) {
  # Ejecutar monitoreo
  monitoreo_day_trading()
} else {
  cat("🛑 No se puede operar más hoy debido a límites\n")
}

cat("\n📱 DAY TRADING MONITOR COMPLETADO\n")
cat("==================================\n")
cat("💡 Ejecutar cada 15-30 minutos durante horario de trading\n")
cat("🕐 Horario recomendado: 8:00 - 20:00\n")
cat("⚠️ Recordar: Máximo 3 operaciones por día\n")
