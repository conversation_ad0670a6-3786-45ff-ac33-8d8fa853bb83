# =============================================================================
# SCALPING MASTER - SISTEMA PRINCIPAL CON MENÚ INTERACTIVO
# =============================================================================
# Sistema completo de scalping con Yahoo Finance y menú principal
# =============================================================================

cat("🚀 SCALPING MASTER - SISTEMA COMPLETO\n")
cat("=====================================\n")
cat("Yahoo Finance • Sin límites • Guía XTB\n\n")

# =============================================================================
# VERIFICAR ARCHIVOS DEL SISTEMA
# =============================================================================

verificar_sistema <- function() {
  archivos_necesarios <- c(
    "estrategia_scalping_yahoo.R",
    "sistema_alertas_yahoo.R",
    "test_yahoo_connection.R"
  )
  
  archivos_faltantes <- c()
  for (archivo in archivos_necesarios) {
    if (!file.exists(archivo)) {
      archivos_faltantes <- c(archivos_faltantes, archivo)
    }
  }
  
  if (length(archivos_faltantes) > 0) {
    cat("❌ ARCHIVOS FALTANTES:\n")
    for (archivo in archivos_faltantes) {
      cat("   •", archivo, "\n")
    }
    cat("\n💡 Ejecuta setup_scalping_yahoo.R primero\n")
    return(FALSE)
  }
  
  return(TRUE)
}

# =============================================================================
# MENÚ PRINCIPAL
# =============================================================================

mostrar_menu_principal <- function() {
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("🎯 SCALPING MASTER - MENÚ PRINCIPAL\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")
  cat("1. 🧪 Probar conexión Yahoo Finance\n")
  cat("2. 📊 Análisis scalping completo\n")
  cat("3. ⚡ Análisis scalping express\n")
  cat("4. 🚨 Monitoreo manual con alertas\n")
  cat("5. 🤖 Monitoreo automático (continuo)\n")
  cat("6. 📈 Análisis con pares secundarios\n")
  cat("7. 📝 Ver últimas alertas\n")
  cat("8. 📊 Estadísticas del sistema\n")
  cat("9. ⚙️ Configuración\n")
  cat("10. 📚 Ayuda y documentación\n")
  cat("11. 🚪 Salir\n")
  cat(paste(rep("-", 70), collapse = ""), "\n")
  cat("Selecciona una opción (1-11): ")
}

# =============================================================================
# FUNCIONES DEL MENÚ
# =============================================================================

opcion_1_test_conexion <- function() {
  cat("\n🧪 PROBANDO CONEXIÓN YAHOO FINANCE\n")
  cat("==================================\n")
  source("test_yahoo_connection.R")
}

opcion_2_analisis_completo <- function() {
  cat("\n📊 ANÁLISIS SCALPING COMPLETO\n")
  cat("=============================\n")
  source("estrategia_scalping_yahoo.R")
  oportunidades <- analisis_scalping_yahoo()
  
  if (length(oportunidades) > 0) {
    cat("\n💡 ¿Quieres activar alertas para estas oportunidades? (s/n): ")
    respuesta <- readline()
    if (tolower(respuesta) == "s") {
      source("sistema_alertas_yahoo.R")
      for (par in names(oportunidades)) {
        escribir_alerta(par, oportunidades[[par]])
      }
      cat("✅ Alertas guardadas\n")
    }
  }
}

opcion_3_analisis_express <- function() {
  cat("\n⚡ ANÁLISIS SCALPING EXPRESS\n")
  cat("===========================\n")
  source("estrategia_scalping_yahoo.R")
  scalping_express()
}

opcion_4_monitoreo_manual <- function() {
  cat("\n🚨 MONITOREO MANUAL CON ALERTAS\n")
  cat("===============================\n")
  source("sistema_alertas_yahoo.R")
  monitoreo_manual_alertas()
}

opcion_5_monitoreo_automatico <- function() {
  cat("\n🤖 MONITOREO AUTOMÁTICO\n")
  cat("=======================\n")
  
  cat("⚠️ IMPORTANTE:\n")
  cat("• El monitoreo automático puede ejecutarse durante horas\n")
  cat("• Se generarán alertas sonoras y visuales\n")
  cat("• Los logs se guardarán automáticamente\n")
  cat("• Presiona Ctrl+C para detener en cualquier momento\n\n")
  
  cat("¿Continuar con monitoreo automático? (s/n): ")
  respuesta <- readline()
  
  if (tolower(respuesta) == "s") {
    source("sistema_alertas_yahoo.R")
    monitoreo_automatico_alertas()
  }
}

opcion_6_pares_secundarios <- function() {
  cat("\n📈 ANÁLISIS CON PARES SECUNDARIOS\n")
  cat("=================================\n")
  cat("Incluyendo pares de mayor volatilidad...\n")
  source("estrategia_scalping_yahoo.R")
  analisis_scalping_yahoo(incluir_secundarios = TRUE)
}

opcion_7_ver_alertas <- function() {
  cat("\n📝 ÚLTIMAS ALERTAS\n")
  cat("==================\n")
  
  archivo_alertas <- "logs/alertas_scalping.txt"
  
  if (file.exists(archivo_alertas)) {
    tryCatch({
      lineas <- readLines(archivo_alertas)
      
      if (length(lineas) == 0) {
        cat("📭 No hay alertas registradas\n")
      } else {
        # Mostrar últimas 30 líneas
        inicio <- max(1, length(lineas) - 30)
        ultimas_lineas <- lineas[inicio:length(lineas)]
        
        cat("📋 Últimas alertas:\n")
        cat(paste(rep("-", 50), collapse = ""), "\n")
        for (linea in ultimas_lineas) {
          cat(linea, "\n")
        }
      }
    }, error = function(e) {
      cat("❌ Error leyendo alertas:", e$message, "\n")
    })
  } else {
    cat("📭 No hay archivo de alertas\n")
    cat("💡 Ejecuta monitoreo para generar alertas\n")
  }
}

opcion_8_estadisticas <- function() {
  cat("\n📊 ESTADÍSTICAS DEL SISTEMA\n")
  cat("===========================\n")
  
  # Estadísticas de archivos
  archivos_info <- list(
    "Alertas" = "logs/alertas_scalping.txt",
    "Log monitoreo" = "logs/monitoreo_alertas.log",
    "Log sistema" = "logs/sistema.log"
  )
  
  for (nombre in names(archivos_info)) {
    archivo <- archivos_info[[nombre]]
    if (file.exists(archivo)) {
      info <- file.info(archivo)
      cat("📁", nombre, ":\n")
      cat("   Tamaño:", round(info$size / 1024, 2), "KB\n")
      cat("   Modificado:", format(info$mtime, "%Y-%m-%d %H:%M:%S"), "\n")
      
      # Contar líneas si es archivo de texto
      if (grepl("\\.txt$|\\.log$", archivo)) {
        tryCatch({
          lineas <- readLines(archivo)
          cat("   Líneas:", length(lineas), "\n")
        }, error = function(e) {})
      }
      cat("\n")
    } else {
      cat("📁", nombre, ": No existe\n\n")
    }
  }
  
  # Estadísticas de tiempo
  cat("⏰ INFORMACIÓN DE SESIÓN:\n")
  cat("   Hora actual:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
  cat("   Zona horaria:", Sys.timezone(), "\n")
  
  # Verificar horario de trading
  source("sistema_alertas_yahoo.R")
  if (en_horario_trading()) {
    cat("   Estado mercado: ✅ ACTIVO\n")
  } else {
    cat("   Estado mercado: 😴 INACTIVO\n")
  }
}

opcion_9_configuracion <- function() {
  cat("\n⚙️ CONFIGURACIÓN DEL SISTEMA\n")
  cat("============================\n")
  
  if (file.exists("config/config_inicial.rds")) {
    config <- readRDS("config/config_inicial.rds")
    
    cat("📊 CONFIGURACIÓN ACTUAL:\n")
    cat("   Capital demo: $", config$capital_demo, "\n")
    cat("   Riesgo por operación:", config$riesgo_por_operacion_pct, "%\n")
    cat("   Stop Loss:", config$stop_loss_pips, "pips\n")
    cat("   Take Profit:", config$take_profit_pips, "pips\n")
    cat("   Max operaciones:", config$max_operaciones_simultaneas, "\n")
    cat("   EMA rápida:", config$ema_rapida, "\n")
    cat("   EMA lenta:", config$ema_lenta, "\n")
    cat("   RSI período:", config$rsi_periodo, "\n\n")
    
    cat("💡 Para modificar la configuración, edita config/config_inicial.rds\n")
  } else {
    cat("❌ Archivo de configuración no encontrado\n")
    cat("💡 Ejecuta setup_scalping_yahoo.R para crear configuración\n")
  }
}

opcion_10_ayuda <- function() {
  cat("\n📚 AYUDA Y DOCUMENTACIÓN\n")
  cat("========================\n")
  
  cat("🎯 ESTRATEGIA SCALPING:\n")
  cat("• Basada en EMA 8/21, RSI 14, Bollinger Bands\n")
  cat("• Timeframe: Datos diarios con análisis técnico\n")
  cat("• Objetivo: 50-100 pips por operación\n")
  cat("• Ratio Risk:Reward: 1:2\n\n")
  
  cat("💰 GESTIÓN DE CAPITAL:\n")
  cat("• Riesgo por operación: 1% del capital\n")
  cat("• Stop Loss: 50 pips\n")
  cat("• Take Profit: 100 pips\n")
  cat("• Máximo 2 operaciones simultáneas\n\n")
  
  cat("⏰ HORARIOS RECOMENDADOS (UTC):\n")
  cat("• Sesión Londres: 08:00 - 17:00\n")
  cat("• Sesión Nueva York: 13:00 - 22:00\n")
  cat("• Mejor momento: 13:00 - 17:00 (solapamiento)\n\n")
  
  cat("📊 PARES RECOMENDADOS:\n")
  cat("• EUR/USD: Spread bajo, alta liquidez\n")
  cat("• GBP/USD: Volatilidad media-alta\n")
  cat("• USD/JPY: Movimientos consistentes\n")
  cat("• AUD/USD: Buena para tendencias\n\n")
  
  cat("🔧 ARCHIVOS DEL SISTEMA:\n")
  cat("• scalping_master.R - Sistema principal (este archivo)\n")
  cat("• estrategia_scalping_yahoo.R - Estrategia de scalping\n")
  cat("• sistema_alertas_yahoo.R - Sistema de alertas\n")
  cat("• test_yahoo_connection.R - Pruebas de conexión\n")
  cat("• setup_scalping_yahoo.R - Configuración inicial\n\n")
  
  cat("📁 DIRECTORIOS:\n")
  cat("• logs/ - Archivos de log y alertas\n")
  cat("• data/ - Datos guardados\n")
  cat("• config/ - Configuraciones\n")
  cat("• reports/ - Reportes (futuro)\n\n")
  
  cat("⚠️ ADVERTENCIAS:\n")
  cat("• Practicar en cuenta demo antes de operar real\n")
  cat("• Nunca arriesgar más del 2% por operación\n")
  cat("• Verificar siempre que es cuenta demo en XTB\n")
  cat("• Cerrar operaciones antes de noticias importantes\n\n")
  
  cat("📧 SOPORTE:\n")
  cat("• README.md - Documentación completa\n")
  cat("• logs/sistema.log - Log de errores\n")
  cat("• Verificar conexión con opción 1\n\n")
}

# =============================================================================
# FUNCIÓN PRINCIPAL
# =============================================================================

ejecutar_scalping_master <- function() {
  # Verificar sistema
  if (!verificar_sistema()) {
    return()
  }
  
  cat("🎉 Sistema verificado correctamente\n")
  
  # Bucle principal del menú
  while (TRUE) {
    mostrar_menu_principal()
    opcion <- readline()
    
    switch(opcion,
      "1" = opcion_1_test_conexion(),
      "2" = opcion_2_analisis_completo(),
      "3" = opcion_3_analisis_express(),
      "4" = opcion_4_monitoreo_manual(),
      "5" = opcion_5_monitoreo_automatico(),
      "6" = opcion_6_pares_secundarios(),
      "7" = opcion_7_ver_alertas(),
      "8" = opcion_8_estadisticas(),
      "9" = opcion_9_configuracion(),
      "10" = opcion_10_ayuda(),
      "11" = {
        cat("\n👋 ¡Hasta luego! Happy Trading!\n")
        cat("💡 Recuerda: Practica en demo antes de operar real\n")
        break
      },
      {
        cat("\n❌ Opción no válida. Selecciona 1-11.\n")
      }
    )
    
    if (opcion != "11") {
      cat("\nPresiona Enter para continuar...")
      readline()
    }
  }
}

# =============================================================================
# INICIO AUTOMÁTICO
# =============================================================================

cat("🎯 SCALPING MASTER LISTO\n")
cat("========================\n")
cat("Sistema completo de scalping con Yahoo Finance\n")
cat("Sin límites de API • Alertas automáticas • Guía XTB\n\n")

cat("🚀 INICIO RÁPIDO:\n")
cat("• Sistema completo: ejecutar_scalping_master()\n")
cat("• Análisis rápido: source('estrategia_scalping_yahoo.R'); scalping_express()\n")
cat("• Solo alertas: source('sistema_alertas_yahoo.R'); monitoreo_manual_alertas()\n\n")

cat("📚 DOCUMENTACIÓN:\n")
cat("• README.md - Guía completa\n")
cat("• Opción 10 del menú - Ayuda detallada\n\n")

# Auto-ejecutar si se desea
cat("¿Iniciar Scalping Master ahora? (s/n): ")
respuesta <- readline()
if (tolower(respuesta) == "s") {
  ejecutar_scalping_master()
} else {
  cat("\n💡 Para iniciar manualmente: ejecutar_scalping_master()\n")
}
