# =============================================================================
# INICIO RÁPIDO - SISTEMA COMPLETO DE SCALPING
# =============================================================================
# Script principal para iniciar el sistema completo de scalping
# con Alpha Vantage y estrategia Triple Confirmación
# =============================================================================

cat("🚀 SISTEMA COMPLETO DE SCALPING\n")
cat("===============================\n")
cat("Alpha Vantage + Estrategia Triple Confirmación + Alertas\n\n")

# =============================================================================
# MENÚ PRINCIPAL
# =============================================================================

mostrar_menu <- function() {
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("🎯 MENÚ PRINCIPAL - SISTEMA SCALPING\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  cat("1. 🔧 Configuración inicial (primera vez)\n")
  cat("2. 🧪 Prueba rápida de conexión\n")
  cat("3. 📊 Monitoreo manual (una vez)\n")
  cat("4. 🤖 Monitoreo automático (continuo)\n")
  cat("5. ⚙️ Configurar alertas\n")
  cat("6. 📈 Ver últimas alertas\n")
  cat("7. 📚 Ayuda y documentación\n")
  cat("8. 🚪 Salir\n")
  cat(paste(rep("-", 60), collapse = ""), "\n")
  cat("Selecciona una opción (1-8): ")
}

# =============================================================================
# FUNCIONES DEL MENÚ
# =============================================================================

configuracion_inicial <- function() {
  cat("\n🔧 CONFIGURACIÓN INICIAL\n")
  cat("========================\n")
  
  cat("📦 Paso 1: Instalando paquetes necesarios...\n")
  source("setup_alpha_vantage.R")
  
  cat("\n✅ Configuración inicial completada\n")
  cat("💡 Próximo paso: Obtener API key de Alpha Vantage\n")
  cat("🔗 https://www.alphavantage.co/support/#api-key\n")
}

prueba_rapida <- function() {
  cat("\n🧪 PRUEBA RÁPIDA DE CONEXIÓN\n")
  cat("============================\n")
  source("test_alpha_vantage_rapido.R")
}

monitoreo_manual <- function() {
  cat("\n📊 MONITOREO MANUAL\n")
  cat("===================\n")
  
  # Verificar si existe la estrategia
  if (!file.exists("estrategia_scalping_triple_confirmacion.R")) {
    cat("❌ Archivo de estrategia no encontrado\n")
    return()
  }
  
  source("estrategia_scalping_triple_confirmacion.R")
  
  # Verificar API key
  if (ESTRATEGIA_CONFIG$api_key == "KDF6QXHG5UOYNHJK") {
    cat("⚠️ Usando API key REAL - datos limitados\n")
    cat("¿Continuar? (s/n): ")
    respuesta <- readline()
    if (tolower(respuesta) != "s") return()
  }
  
  señales <- monitoreo_triple_confirmacion()
  
  if (length(señales) > 0) {
    cat("\n🎯 ¿Quieres activar alertas para estas señales? (s/n): ")
    respuesta <- readline()
    if (tolower(respuesta) == "s") {
      source("sistema_alertas_scalping.R")
      for (par in names(señales)) {
        escribir_alerta_archivo(par, señales[[par]])
      }
    }
  }
}

monitoreo_automatico <- function() {
  cat("\n🤖 MONITOREO AUTOMÁTICO\n")
  cat("=======================\n")
  
  # Verificar archivos necesarios
  archivos_necesarios <- c(
    "estrategia_scalping_triple_confirmacion.R",
    "sistema_alertas_scalping.R"
  )
  
  for (archivo in archivos_necesarios) {
    if (!file.exists(archivo)) {
      cat("❌ Archivo necesario no encontrado:", archivo, "\n")
      return()
    }
  }
  
  cat("⚠️ IMPORTANTE:\n")
  cat("• El monitoreo automático puede ejecutarse durante horas\n")
  cat("• Asegúrate de tener conexión estable a internet\n")
  cat("• Verificar límites de API (5 llamadas/min en plan gratuito)\n")
  cat("• Se generarán alertas sonoras y visuales\n\n")
  
  cat("¿Continuar con monitoreo automático? (s/n): ")
  respuesta <- readline()
  
  if (tolower(respuesta) == "s") {
    source("sistema_alertas_scalping.R")
    monitoreo_automatico_con_alertas()
  }
}

configurar_alertas_menu <- function() {
  cat("\n⚙️ CONFIGURACIÓN DE ALERTAS\n")
  cat("===========================\n")
  
  if (!file.exists("sistema_alertas_scalping.R")) {
    cat("❌ Sistema de alertas no encontrado\n")
    return()
  }
  
  source("sistema_alertas_scalping.R")
  configurar_alertas()
}

ver_ultimas_alertas <- function() {
  cat("\n📈 ÚLTIMAS ALERTAS\n")
  cat("==================\n")
  
  archivo_alertas <- "alertas_scalping.txt"
  
  if (!file.exists(archivo_alertas)) {
    cat("📭 No hay alertas registradas aún\n")
    cat("💡 Ejecuta monitoreo para generar alertas\n")
    return()
  }
  
  tryCatch({
    lineas <- readLines(archivo_alertas)
    
    if (length(lineas) == 0) {
      cat("📭 Archivo de alertas vacío\n")
      return()
    }
    
    # Mostrar últimas 50 líneas
    inicio <- max(1, length(lineas) - 50)
    ultimas_lineas <- lineas[inicio:length(lineas)]
    
    cat("📋 Últimas alertas (", length(ultimas_lineas), " líneas):\n")
    cat(paste(rep("-", 50), collapse = ""), "\n")
    
    for (linea in ultimas_lineas) {
      cat(linea, "\n")
    }
    
  }, error = function(e) {
    cat("❌ Error leyendo alertas:", e$message, "\n")
  })
}

mostrar_ayuda <- function() {
  cat("\n📚 AYUDA Y DOCUMENTACIÓN\n")
  cat("========================\n")
  
  cat("🎯 ESTRATEGIA TRIPLE CONFIRMACIÓN:\n")
  cat("• EMA 8/21: Dirección de tendencia\n")
  cat("• RSI 14: Momentum y puntos de entrada\n")
  cat("• Bandas Bollinger: Volatilidad y niveles\n")
  cat("• Mínimo 2 confirmaciones de 3 indicadores\n\n")
  
  cat("💰 GESTIÓN DE CAPITAL:\n")
  cat("• Riesgo por operación: 0.5%\n")
  cat("• Stop Loss: 8 pips\n")
  cat("• Take Profit: 16 pips (ratio 1:2)\n")
  cat("• Máximo 2 operaciones simultáneas\n\n")
  
  cat("⏰ HORARIOS RECOMENDADOS (UTC):\n")
  cat("• Sesión Londres: 08:00 - 17:00\n")
  cat("• Sesión Nueva York: 13:00 - 22:00\n")
  cat("• Solapamiento Londres-NY: 13:00 - 17:00 (MEJOR)\n\n")
  
  cat("📊 PARES RECOMENDADOS:\n")
  cat("• EUR/USD: Spread bajo, alta liquidez\n")
  cat("• GBP/USD: Volatilidad media-alta\n")
  cat("• USD/JPY: Movimientos consistentes\n")
  cat("• GBP/JPY: Alta volatilidad (avanzado)\n\n")
  
  cat("🔧 CONFIGURACIÓN ALPHA VANTAGE:\n")
  cat("• Plan gratuito: 5 llamadas/min, 500/día\n")
  cat("• Datos con retraso ~15 minutos\n")
  cat("• Para scalping real: considerar plan premium\n")
  cat("• API key: https://www.alphavantage.co/support/#api-key\n\n")
  
  cat("📁 ARCHIVOS DEL SISTEMA:\n")
  cat("• setup_alpha_vantage.R: Configuración inicial\n")
  cat("• estrategia_scalping_triple_confirmacion.R: Estrategia principal\n")
  cat("• sistema_alertas_scalping.R: Sistema de alertas\n")
  cat("• test_alpha_vantage_rapido.R: Pruebas de conexión\n")
  cat("• alertas_scalping.txt: Registro de alertas\n")
  cat("• log_monitoreo.txt: Log del sistema\n\n")
  
  cat("⚠️ ADVERTENCIAS:\n")
  cat("• Este sistema es para fines educativos\n")
  cat("• Siempre verificar señales manualmente\n")
  cat("• Usar capital que puedas permitirte perder\n")
  cat("• Practicar en cuenta demo antes de operar real\n")
  cat("• Los resultados pasados no garantizan resultados futuros\n\n")
}

# =============================================================================
# FUNCIÓN PRINCIPAL
# =============================================================================

ejecutar_sistema_scalping <- function() {
  cat("🚀 Bienvenido al Sistema de Scalping\n")
  cat("Versión: 1.0 | Alpha Vantage + Triple Confirmación\n")
  
  # Verificar archivos del sistema
  archivos_sistema <- c(
    "setup_alpha_vantage.R",
    "estrategia_scalping_triple_confirmacion.R", 
    "sistema_alertas_scalping.R",
    "test_alpha_vantage_rapido.R"
  )
  
  archivos_faltantes <- c()
  for (archivo in archivos_sistema) {
    if (!file.exists(archivo)) {
      archivos_faltantes <- c(archivos_faltantes, archivo)
    }
  }
  
  if (length(archivos_faltantes) > 0) {
    cat("❌ ARCHIVOS FALTANTES:\n")
    for (archivo in archivos_faltantes) {
      cat("   •", archivo, "\n")
    }
    cat("\n💡 Asegúrate de tener todos los archivos del sistema\n")
    return()
  }
  
  # Bucle principal del menú
  while (TRUE) {
    mostrar_menu()
    opcion <- readline()
    
    switch(opcion,
      "1" = configuracion_inicial(),
      "2" = prueba_rapida(),
      "3" = monitoreo_manual(),
      "4" = monitoreo_automatico(),
      "5" = configurar_alertas_menu(),
      "6" = ver_ultimas_alertas(),
      "7" = mostrar_ayuda(),
      "8" = {
        cat("\n👋 ¡Hasta luego! Happy Trading!\n")
        break
      },
      {
        cat("\n❌ Opción no válida. Selecciona 1-8.\n")
      }
    )
    
    if (opcion != "8") {
      cat("\nPresiona Enter para continuar...")
      readline()
    }
  }
}

# =============================================================================
# INICIO AUTOMÁTICO
# =============================================================================

cat("🎯 SISTEMA LISTO\n")
cat("================\n")
cat("Para iniciar el sistema ejecuta: ejecutar_sistema_scalping()\n")
cat("O selecciona una opción específica:\n\n")

cat("🔧 Primera vez: source('setup_alpha_vantage.R')\n")
cat("🧪 Prueba rápida: source('test_alpha_vantage_rapido.R')\n")
cat("📊 Monitoreo: source('estrategia_scalping_triple_confirmacion.R')\n")
cat("🤖 Alertas: source('sistema_alertas_scalping.R')\n\n")

cat("💡 CONSEJO: Ejecuta ejecutar_sistema_scalping() para el menú completo\n\n")

# Auto-ejecutar si se desea
cat("¿Iniciar el sistema ahora? (s/n): ")
respuesta <- readline()
if (tolower(respuesta) == "s") {
  ejecutar_sistema_scalping()
}
