# =============================================================================
# ALPHA VANTAGE SCALPING - CONEXIÓN EN TIEMPO REAL
# =============================================================================
# Sistema completo para scalping con datos intraday de Alpha Vantage
# Datos de 1min, 5min, 15min para trading de alta frecuencia
# =============================================================================

library(httr)
library(jsonlite)
library(quantmod)
library(TTR)
library(dplyr)

cat("🚀 ALPHA VANTAGE SCALPING SYSTEM\n")
cat("=================================\n")
cat("Sistema de datos en tiempo real para scalping\n")
cat("Timeframes: 1min, 5min, 15min\n\n")

# =============================================================================
# CONFIGURACIÓN ALPHA VANTAGE
# =============================================================================

# ⚠️ IMPORTANTE: Obtén tu API key gratuita en: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_CONFIG <- list(
  api_key = "KDF6QXHG5UOYNHJK",  # 🔑 CAMBIAR POR TU API KEY REAL
  base_url = "https://www.alphavantage.co/query",
  rate_limit_calls_per_minute = 5,  # Plan gratuito: 5 llamadas/min
  rate_limit_calls_per_day = 500,   # Plan gratuito: 500 llamadas/día
  timeout_seconds = 30
)

# =============================================================================
# CONFIGURACIÓN DE SCALPING
# =============================================================================

SCALPING_CONFIG <- list(
  # Gestión de capital para scalping
  capital_total = 20000,
  capital_por_operacion_pct = 0.05,  # 5% por operación (más agresivo)
  riesgo_por_operacion_pct = 0.01,   # 1% riesgo por operación
  stop_loss_pips = 10,               # 10 pips stop loss
  take_profit_pips = 20,             # 20 pips take profit (1:2 ratio)
  max_operaciones_simultaneas = 3,
  
  # Pares para scalping (mayor volatilidad)
  pares_scalping = list(
    "EUR/USD" = "EURUSD",
    "GBP/USD" = "GBPUSD", 
    "USD/JPY" = "USDJPY",
    "GBP/JPY" = "GBPJPY",  # Muy volátil para scalping
    "EUR/GBP" = "EURGBP"
  ),
  
  # Timeframes para scalping
  timeframes = c("1min", "5min", "15min"),
  timeframe_principal = "1min",  # Principal para scalping
  
  # Indicadores para scalping rápido
  ema_rapida = 8,
  ema_lenta = 21,
  rsi_periodo = 14,
  rsi_sobrecompra = 70,
  rsi_sobreventa = 30,
  bollinger_periodo = 20,
  bollinger_sd = 2
)

# =============================================================================
# FUNCIONES DE CONEXIÓN ALPHA VANTAGE
# =============================================================================

# Función para verificar API key
verificar_api_key <- function() {
  if (ALPHA_VANTAGE_CONFIG$api_key == "KDF6QXHG5UOYNHJK") {
    cat("⚠️ USANDO API KEY DEMO - DATOS LIMITADOS\n")
    cat("📝 Para datos completos:\n")
    cat("   1. Visita: https://www.alphavantage.co/support/#api-key\n")
    cat("   2. Obtén tu API key gratuita\n")
    cat("   3. Reemplaza 'DEMO' en ALPHA_VANTAGE_CONFIG$api_key\n\n")
    return(FALSE)
  }
  return(TRUE)
}

# Función para obtener datos intraday
obtener_datos_intraday_av <- function(simbolo, timeframe = "1min", outputsize = "compact") {
  cat("📡 Obteniendo datos", timeframe, "de", simbolo, "...")
  
  # Construir URL
  url <- paste0(
    ALPHA_VANTAGE_CONFIG$base_url,
    "?function=FX_INTRADAY",
    "&from_symbol=", substr(simbolo, 1, 3),
    "&to_symbol=", substr(simbolo, 4, 6),
    "&interval=", timeframe,
    "&outputsize=", outputsize,
    "&apikey=", ALPHA_VANTAGE_CONFIG$api_key
  )
  
  tryCatch({
    # Realizar petición HTTP
    response <- GET(url, timeout(ALPHA_VANTAGE_CONFIG$timeout_seconds))
    
    if (status_code(response) != 200) {
      cat(" ❌ Error HTTP:", status_code(response), "\n")
      return(NULL)
    }
    
    # Parsear JSON
    data <- fromJSON(content(response, "text"))
    
    # Verificar errores de API
    if ("Error Message" %in% names(data)) {
      cat(" ❌ Error API:", data$`Error Message`, "\n")
      return(NULL)
    }
    
    if ("Note" %in% names(data)) {
      cat(" ⚠️ Límite de API:", data$Note, "\n")
      return(NULL)
    }
    
    # Extraer datos de series temporales
    time_series_key <- paste0("Time Series FX (", timeframe, ")")
    
    if (!time_series_key %in% names(data)) {
      cat(" ❌ No se encontraron datos de series temporales\n")
      return(NULL)
    }
    
    time_series <- data[[time_series_key]]
    
    if (length(time_series) == 0) {
      cat(" ❌ Series temporales vacías\n")
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      DateTime = as.POSIXct(names(time_series)),
      Open = as.numeric(sapply(time_series, function(x) x$`1. open`)),
      High = as.numeric(sapply(time_series, function(x) x$`2. high`)),
      Low = as.numeric(sapply(time_series, function(x) x$`3. low`)),
      Close = as.numeric(sapply(time_series, function(x) x$`4. close`)),
      stringsAsFactors = FALSE
    )
    
    # Ordenar por fecha (más reciente primero)
    df <- df[order(df$DateTime, decreasing = TRUE), ]
    
    cat(" ✅", nrow(df), "registros\n")
    return(df)
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# Función para obtener múltiples timeframes
obtener_datos_multi_timeframe <- function(simbolo) {
  cat("🔄 Obteniendo datos multi-timeframe para", simbolo, "\n")
  
  datos_multi <- list()
  
  for (tf in SCALPING_CONFIG$timeframes) {
    datos_multi[[tf]] <- obtener_datos_intraday_av(simbolo, tf)
    
    # Respetar límites de API
    if (tf != SCALPING_CONFIG$timeframes[length(SCALPING_CONFIG$timeframes)]) {
      cat("⏳ Esperando para respetar límites de API...\n")
      Sys.sleep(12)  # 5 llamadas/min = 12 segundos entre llamadas
    }
  }
  
  return(datos_multi)
}

# =============================================================================
# FUNCIONES DE ANÁLISIS PARA SCALPING
# =============================================================================

# Calcular indicadores para scalping
calcular_indicadores_scalping <- function(datos) {
  if (is.null(datos) || nrow(datos) < 50) {
    return(NULL)
  }
  
  # Ordenar datos cronológicamente para cálculos
  datos <- datos[order(datos$DateTime), ]
  
  # Calcular indicadores
  datos$EMA_Rapida <- EMA(datos$Close, n = SCALPING_CONFIG$ema_rapida)
  datos$EMA_Lenta <- EMA(datos$Close, n = SCALPING_CONFIG$ema_lenta)
  datos$RSI <- RSI(datos$Close, n = SCALPING_CONFIG$rsi_periodo)
  
  # Bandas de Bollinger
  bb <- BBands(datos$Close, n = SCALPING_CONFIG$bollinger_periodo, sd = SCALPING_CONFIG$bollinger_sd)
  datos$BB_Upper <- bb[, "up"]
  datos$BB_Middle <- bb[, "mavg"]
  datos$BB_Lower <- bb[, "dn"]
  
  # Calcular momentum y volatilidad
  datos$Momentum <- c(NA, diff(datos$Close))
  datos$Volatilidad <- runSD(datos$Close, n = 20)
  
  # Volver a ordenar por fecha más reciente primero
  datos <- datos[order(datos$DateTime, decreasing = TRUE), ]
  
  return(datos)
}

# Detectar señales de scalping
detectar_señales_scalping <- function(datos_con_indicadores) {
  if (is.null(datos_con_indicadores) || nrow(datos_con_indicadores) < 10) {
    return(list(señal = "Sin datos", fuerza = 0, precio = NA))
  }
  
  # Datos más recientes (primeras filas)
  ultimo <- datos_con_indicadores[1, ]
  anterior <- datos_con_indicadores[2, ]
  
  señal <- "NEUTRAL"
  fuerza <- 0
  razones <- c()
  
  # Verificar datos válidos
  if (any(is.na(c(ultimo$EMA_Rapida, ultimo$EMA_Lenta, ultimo$RSI)))) {
    return(list(señal = "Sin datos válidos", fuerza = 0, precio = ultimo$Close))
  }
  
  # SEÑALES DE COMPRA
  if (ultimo$EMA_Rapida > ultimo$EMA_Lenta && 
      anterior$EMA_Rapida <= anterior$EMA_Lenta) {
    señal <- "COMPRA"
    fuerza <- fuerza + 3
    razones <- c(razones, "Cruce EMA alcista")
  }
  
  if (ultimo$RSI < SCALPING_CONFIG$rsi_sobreventa && ultimo$RSI > anterior$RSI) {
    if (señal == "NEUTRAL") señal <- "COMPRA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobreventa recuperándose")
  }
  
  if (ultimo$Close < ultimo$BB_Lower && ultimo$Momentum > 0) {
    if (señal == "NEUTRAL") señal <- "COMPRA"
    fuerza <- fuerza + 2
    razones <- c(razones, "Rebote en banda inferior")
  }
  
  # SEÑALES DE VENTA
  if (ultimo$EMA_Rapida < ultimo$EMA_Lenta && 
      anterior$EMA_Rapida >= anterior$EMA_Lenta) {
    señal <- "VENTA"
    fuerza <- fuerza + 3
    razones <- c(razones, "Cruce EMA bajista")
  }
  
  if (ultimo$RSI > SCALPING_CONFIG$rsi_sobrecompra && ultimo$RSI < anterior$RSI) {
    if (señal == "NEUTRAL") señal <- "VENTA"
    fuerza <- fuerza + 2
    razones <- c(razones, "RSI sobrecompra corrigiéndose")
  }
  
  if (ultimo$Close > ultimo$BB_Upper && ultimo$Momentum < 0) {
    if (señal == "NEUTRAL") señal <- "VENTA"
    fuerza <- fuerza + 2
    razones <- c(razones, "Rechazo en banda superior")
  }
  
  return(list(
    señal = señal,
    fuerza = fuerza,
    precio = ultimo$Close,
    rsi = ultimo$RSI,
    datetime = ultimo$DateTime,
    razones = razones
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE MONITOREO
# =============================================================================

monitoreo_scalping_tiempo_real <- function() {
  cat("🎯 INICIANDO MONITOREO DE SCALPING EN TIEMPO REAL\n")
  cat("================================================\n")
  cat("Hora inicio:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
  
  # Verificar API key
  verificar_api_key()
  
  señales_detectadas <- list()
  
  for (i in 1:length(SCALPING_CONFIG$pares_scalping)) {
    nombre_par <- names(SCALPING_CONFIG$pares_scalping)[i]
    simbolo <- SCALPING_CONFIG$pares_scalping[[i]]
    
    cat("\n📊 Analizando", nombre_par, "(", simbolo, ")\n")
    cat(paste(rep("-", 40), collapse = ""), "\n")
    
    # Obtener datos del timeframe principal
    datos_raw <- obtener_datos_intraday_av(simbolo, SCALPING_CONFIG$timeframe_principal)
    
    if (is.null(datos_raw)) {
      cat("❌ No se pudieron obtener datos\n")
      next
    }
    
    # Calcular indicadores
    datos_con_indicadores <- calcular_indicadores_scalping(datos_raw)
    
    if (is.null(datos_con_indicadores)) {
      cat("❌ No se pudieron calcular indicadores\n")
      next
    }
    
    # Detectar señales
    resultado <- detectar_señales_scalping(datos_con_indicadores)
    
    # Mostrar resultado
    cat("🔍 Señal:", resultado$señal, "\n")
    cat("💪 Fuerza:", resultado$fuerza, "/10\n")
    cat("💰 Precio actual:", sprintf("%.5f", resultado$precio), "\n")
    cat("📈 RSI:", sprintf("%.2f", resultado$rsi), "\n")
    
    if (length(resultado$razones) > 0) {
      cat("📋 Razones:\n")
      for (razon in resultado$razones) {
        cat("   •", razon, "\n")
      }
    }
    
    # Guardar señales fuertes
    if (resultado$fuerza >= 3) {
      señales_detectadas[[nombre_par]] <- resultado
      cat("⭐ SEÑAL FUERTE DETECTADA!\n")
    }
    
    # Respetar límites de API entre pares
    if (i < length(SCALPING_CONFIG$pares_scalping)) {
      cat("⏳ Pausa para límites de API...\n")
      Sys.sleep(12)
    }
  }
  
  # Resumen final
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("📊 RESUMEN DE SEÑALES DE SCALPING\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  if (length(señales_detectadas) == 0) {
    cat("😴 No hay señales fuertes en este momento\n")
    cat("💡 Recomendación: Esperar o revisar en 5-15 minutos\n")
  } else {
    cat("🚨 SEÑALES FUERTES DETECTADAS:", length(señales_detectadas), "\n\n")
    
    for (par in names(señales_detectadas)) {
      señal <- señales_detectadas[[par]]
      cat("🎯", par, "- Señal:", señal$señal, "- Fuerza:", señal$fuerza, "\n")
      cat("   Precio:", sprintf("%.5f", señal$precio), "- RSI:", sprintf("%.2f", señal$rsi), "\n")
      cat("   Hora:", format(señal$datetime, "%H:%M:%S"), "\n\n")
    }
  }
  
  cat("⏰ Monitoreo completado:", format(Sys.time(), "%H:%M:%S"), "\n")
  cat("🔄 Para monitoreo continuo, ejecutar cada 1-5 minutos\n")
  
  return(señales_detectadas)
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("\n📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("1. Obtener API key gratuita: https://www.alphavantage.co/support/#api-key\n")
cat("2. Reemplazar 'DEMO' en ALPHA_VANTAGE_CONFIG$api_key\n")
cat("3. Ejecutar: monitoreo_scalping_tiempo_real()\n")
cat("4. Para monitoreo continuo, ejecutar cada 1-5 minutos\n\n")

cat("🎯 EJEMPLO DE USO:\n")
cat("# Cambiar API key\n")
cat("ALPHA_VANTAGE_CONFIG$api_key <- 'TU_API_KEY_AQUI'\n\n")
cat("# Ejecutar monitoreo\n")
cat("señales <- monitoreo_scalping_tiempo_real()\n\n")

cat("⚠️ LÍMITES PLAN GRATUITO:\n")
cat("• 5 llamadas por minuto\n")
cat("• 500 llamadas por día\n")
cat("• Datos con retraso de ~15 minutos\n\n")
