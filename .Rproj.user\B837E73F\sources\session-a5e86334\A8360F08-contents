# =============================================================================
# ESTRATEGIA ROBUSTA XTB - MANEJO DE LÍMITES API
# =============================================================================
# Versión mejorada que maneja límites de API y da alternativas
# =============================================================================

library(httr)
library(jsonlite)

cat("🛡️ ESTRATEGIA ROBUSTA XTB\n")
cat("==========================\n")
cat("Manejo inteligente de límites de API\n\n")

# =============================================================================
# CONFIGURACIÓN ROBUSTA
# =============================================================================

ESTRATEGIA_ROBUSTA <- list(
  api_key = "KDF6QXHG5UOYNHJK",
  
  # Pares principales para monitoreo
  pares_principales = list(
    "EUR/USD" = "EURUSD",
    "GBP/USD" = "GBPUSD"
  ),
  
  # Configuración de trading
  volumen_demo = 0.10,
  stop_loss_pips = 50,
  take_profit_pips = 100,
  
  # Umbrales para señales
  umbral_movimiento_fuerte = 0.15,  # 0.15%
  umbral_movimiento_medio = 0.08,   # 0.08%
  
  # Control de API
  pausa_entre_llamadas = 15,  # 15 segundos
  max_reintentos = 3
)

# =============================================================================
# FUNCIÓN ROBUSTA PARA OBTENER PRECIO ACTUAL
# =============================================================================

obtener_precio_robusto <- function(simbolo, reintentos = 3) {
  from_currency <- substr(simbolo, 1, 3)
  to_currency <- substr(simbolo, 4, 6)
  
  for (intento in 1:reintentos) {
    cat("📡 Obteniendo precio", simbolo, "(intento", intento, ")...")
    
    url <- paste0(
      "https://www.alphavantage.co/query",
      "?function=CURRENCY_EXCHANGE_RATE",
      "&from_currency=", from_currency,
      "&to_currency=", to_currency,
      "&apikey=", ESTRATEGIA_ROBUSTA$api_key
    )
    
    tryCatch({
      response <- GET(url, timeout(20))
      
      if (status_code(response) == 200) {
        data <- fromJSON(content(response, "text", encoding = "UTF-8"))
        
        if ("Realtime Currency Exchange Rate" %in% names(data)) {
          rate_data <- data$`Realtime Currency Exchange Rate`
          precio <- as.numeric(rate_data$`5. Exchange Rate`)
          
          cat(" ✅", sprintf("%.5f", precio), "\n")
          
          return(list(
            precio = precio,
            bid = as.numeric(rate_data$`8. Bid Price`),
            ask = as.numeric(rate_data$`9. Ask Price`),
            timestamp = rate_data$`6. Last Refreshed`,
            par = simbolo
          ))
        } else if ("Note" %in% names(data)) {
          cat(" ⚠️ Límite API\n")
          if (intento < reintentos) {
            cat("   Esperando 60 segundos...\n")
            Sys.sleep(60)
          }
        } else {
          cat(" ❌ Formato inesperado\n")
        }
      } else {
        cat(" ❌ HTTP", status_code(response), "\n")
      }
      
    }, error = function(e) {
      cat(" ❌ Error:", e$message, "\n")
    })
    
    if (intento < reintentos) {
      Sys.sleep(ESTRATEGIA_ROBUSTA$pausa_entre_llamadas)
    }
  }
  
  return(NULL)
}

# =============================================================================
# ANÁLISIS SIMPLE BASADO EN MOMENTUM
# =============================================================================

analizar_momentum_simple <- function(precios_historicos) {
  if (length(precios_historicos) < 2) {
    return(list(señal = "SIN_DATOS", fuerza = 0))
  }
  
  # Calcular cambio porcentual
  precio_actual <- precios_historicos[length(precios_historicos)]
  precio_anterior <- precios_historicos[length(precios_historicos) - 1]
  
  cambio_pct <- ((precio_actual - precio_anterior) / precio_anterior) * 100
  
  señal <- "NEUTRAL"
  fuerza <- 0
  
  # Análisis de momentum
  if (abs(cambio_pct) >= ESTRATEGIA_ROBUSTA$umbral_movimiento_fuerte) {
    fuerza <- 3
    if (cambio_pct > 0) {
      señal <- "COMPRA_MOMENTUM_FUERTE"
    } else {
      señal <- "VENTA_MOMENTUM_FUERTE"
    }
  } else if (abs(cambio_pct) >= ESTRATEGIA_ROBUSTA$umbral_movimiento_medio) {
    fuerza <- 2
    if (cambio_pct > 0) {
      señal <- "COMPRA_MOMENTUM"
    } else {
      señal <- "VENTA_MOMENTUM"
    }
  }
  
  return(list(
    señal = señal,
    fuerza = fuerza,
    cambio_pct = cambio_pct,
    precio_actual = precio_actual
  ))
}

# =============================================================================
# MONITOREO INTELIGENTE CON HISTORIAL
# =============================================================================

# Variable global para almacenar historial de precios
if (!exists("historial_precios")) {
  historial_precios <- list()
}

monitoreo_inteligente_xtb <- function() {
  cat("🧠 MONITOREO INTELIGENTE XTB\n")
  cat("============================\n")
  cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")
  
  oportunidades <- list()
  
  for (par_nombre in names(ESTRATEGIA_ROBUSTA$pares_principales)) {
    simbolo <- ESTRATEGIA_ROBUSTA$pares_principales[[par_nombre]]
    
    cat("📊", par_nombre, "\n")
    cat(paste(rep("-", 25), collapse = ""), "\n")
    
    # Obtener precio actual
    precio_data <- obtener_precio_robusto(simbolo)
    
    if (!is.null(precio_data)) {
      # Actualizar historial
      if (is.null(historial_precios[[simbolo]])) {
        historial_precios[[simbolo]] <<- c()
      }
      
      historial_precios[[simbolo]] <<- c(historial_precios[[simbolo]], precio_data$precio)
      
      # Mantener solo últimos 10 precios
      if (length(historial_precios[[simbolo]]) > 10) {
        historial_precios[[simbolo]] <<- tail(historial_precios[[simbolo]], 10)
      }
      
      # Analizar momentum si tenemos suficiente historial
      if (length(historial_precios[[simbolo]]) >= 2) {
        analisis <- analizar_momentum_simple(historial_precios[[simbolo]])
        
        cat("💰 Precio actual:", sprintf("%.5f", precio_data$precio), "\n")
        cat("📊 Cambio:", sprintf("%+.3f%%", analisis$cambio_pct), "\n")
        cat("🔍 Señal:", analisis$señal, "\n")
        cat("💪 Fuerza:", analisis$fuerza, "\n")
        
        # Guardar oportunidades
        if (analisis$fuerza >= 2) {
          oportunidades[[par_nombre]] <- list(
            precio_data = precio_data,
            analisis = analisis
          )
          cat("⭐ OPORTUNIDAD DETECTADA!\n")
        }
      } else {
        cat("💰 Precio actual:", sprintf("%.5f", precio_data$precio), "\n")
        cat("📊 Construyendo historial... (", length(historial_precios[[simbolo]]), "/2)\n")
      }
    } else {
      cat("❌ No se pudo obtener precio\n")
    }
    
    cat("\n")
    
    # Pausa entre pares
    if (par_nombre != names(ESTRATEGIA_ROBUSTA$pares_principales)[length(ESTRATEGIA_ROBUSTA$pares_principales)]) {
      cat("⏳ Pausa entre pares...\n")
      Sys.sleep(ESTRATEGIA_ROBUSTA$pausa_entre_llamadas)
    }
  }
  
  # Generar guía si hay oportunidades
  if (length(oportunidades) > 0) {
    generar_guia_xtb_robusta(oportunidades)
  } else {
    cat("😴 No hay oportunidades de momentum en este momento\n")
    cat("💡 Ejecuta la función cada 5-10 minutos para construir historial\n")
    cat("📈 Se necesitan al menos 2 lecturas para detectar momentum\n")
  }
  
  return(oportunidades)
}

# =============================================================================
# GUÍA XTB SIMPLIFICADA
# =============================================================================

generar_guia_xtb_robusta <- function(oportunidades) {
  cat(paste(rep("=", 60), collapse = ""), "\n")
  cat("🎯 GUÍA RÁPIDA PARA XTB\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  operacion_num <- 1
  
  for (par in names(oportunidades)) {
    opp <- oportunidades[[par]]
    precio <- opp$precio_data$precio
    señal <- opp$analisis$señal
    
    cat("🎯 OPERACIÓN", operacion_num, ":", par, "\n")
    cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
    
    if (grepl("COMPRA", señal)) {
      tipo <- "COMPRAR"
      color <- "VERDE"
      sl <- precio - (ESTRATEGIA_ROBUSTA$stop_loss_pips * 0.0001)
      tp <- precio + (ESTRATEGIA_ROBUSTA$take_profit_pips * 0.0001)
    } else {
      tipo <- "VENDER"
      color <- "ROJO"
      sl <- precio + (ESTRATEGIA_ROBUSTA$stop_loss_pips * 0.0001)
      tp <- precio - (ESTRATEGIA_ROBUSTA$take_profit_pips * 0.0001)
    }
    
    cat("📊 RESUMEN:\n")
    cat("   • Acción:", tipo, par, "\n")
    cat("   • Precio:", sprintf("%.5f", precio), "\n")
    cat("   • Momentum:", sprintf("%+.3f%%", opp$analisis$cambio_pct), "\n")
    cat("   • Fuerza:", opp$analisis$fuerza, "/3\n\n")
    
    cat("🎯 EN XTB:\n")
    cat("   1. Buscar:", par, "\n")
    cat("   2. Clic botón", color, ":", tipo, "\n")
    cat("   3. Volumen:", ESTRATEGIA_ROBUSTA$volumen_demo, "lotes\n")
    cat("   4. Stop Loss:", sprintf("%.5f", sl), "\n")
    cat("   5. Take Profit:", sprintf("%.5f", tp), "\n")
    cat("   6. EJECUTAR OPERACIÓN\n\n")
    
    cat("💰 RIESGO/BENEFICIO:\n")
    cat("   • Riesgo: $100 (demo)\n")
    cat("   • Ganancia potencial: $200 (demo)\n")
    cat("   • Ratio: 1:2 ✅\n\n")
    
    operacion_num <- operacion_num + 1
    
    if (operacion_num > 2) break  # Máximo 2 operaciones
  }
  
  cat("⚠️ RECORDATORIOS:\n")
  cat("   • Verificar que es cuenta DEMO\n")
  cat("   • Máximo 2 operaciones simultáneas\n")
  cat("   • Monitorear gráficos después de abrir\n")
  cat("   • Cerrar si hay reversión fuerte\n\n")
}

# =============================================================================
# FUNCIÓN DE RESET PARA EMPEZAR DE NUEVO
# =============================================================================

reset_historial <- function() {
  historial_precios <<- list()
  cat("🔄 Historial de precios reiniciado\n")
  cat("💡 Ejecuta monitoreo_inteligente_xtb() para empezar\n")
}

# =============================================================================
# INSTRUCCIONES
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Monitoreo principal: monitoreo_inteligente_xtb()\n")
cat("• Reiniciar historial: reset_historial()\n")
cat("• Ejecutar cada 5-10 minutos para mejores resultados\n\n")

cat("💡 CÓMO FUNCIONA:\n")
cat("• Construye historial de precios automáticamente\n")
cat("• Detecta momentum comparando precios recientes\n")
cat("• Genera señales cuando hay movimientos significativos\n")
cat("• Maneja límites de API automáticamente\n\n")

cat("🚀 EMPEZAR AHORA:\n")
cat("monitoreo_inteligente_xtb()\n\n")

cat("⚠️ NOTA IMPORTANTE:\n")
cat("• La primera ejecución construye historial\n")
cat("• Las siguientes ejecuciones detectan momentum\n")
cat("• Espera 15 segundos entre llamadas para evitar límites\n\n")
