# =============================================================================
# ESTRATEGIA SCALPING YAHOO FINANCE - SISTEMA PRINCIPAL
# =============================================================================
# Estrategia completa de scalping con Yahoo Finance y guía para XTB
# =============================================================================

library(quantmod)
library(TTR)
library(dplyr)

cat("🎯 ESTRATEGIA SCALPING YAHOO FINANCE\n")
cat("====================================\n")
cat("Sistema profesional de scalping con guía XTB\n\n")

# =============================================================================
# CONFIGURACIÓN DE LA ESTRATEGIA
# =============================================================================

SCALPING_CONFIG <- list(
  # Pares de divisas principales
  pares_principales = list(
    "EUR/USD" = "EURUSD=X",
    "GBP/USD" = "GBPUSD=X", 
    "USD/JPY" = "USDJPY=X",
    "AUD/USD" = "AUDUSD=X"
  ),
  
  # Pares secundarios (mayor volatilidad)
  pares_secundarios = list(
    "USD/CHF" = "USDCHF=X",
    "EUR/GBP" = "EURGBP=X",
    "GBP/JPY" = "GBPJPY=X"
  ),
  
  # Indicadores técnicos
  ema_rapida = 8,
  ema_lenta = 21,
  rsi_periodo = 14,
  rsi_sobrecompra = 70,
  rsi_sobreventa = 30,
  bb_periodo = 20,
  bb_desviaciones = 2,
  
  # Gestión de capital
  capital_demo = 10000,
  riesgo_por_operacion_pct = 1.0,  # 1% por operación
  reward_risk_ratio = 2.0,         # 1:2 ratio
  max_operaciones_simultaneas = 2,
  
  # Stop Loss y Take Profit (en pips)
  stop_loss_pips = 50,
  take_profit_pips = 100,
  
  # Configuración XTB
  volumen_demo = 0.10,  # 0.10 lotes para demo
  
  # Filtros de calidad
  min_fuerza_señal = 2,
  min_volatilidad = 0.05,  # 0.05% mínimo de volatilidad
  
  # Días de datos para análisis
  dias_analisis = 30
)

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS DE YAHOO FINANCE
# =============================================================================

obtener_datos_yahoo <- function(simbolo, dias = SCALPING_CONFIG$dias_analisis) {
  cat("📡 Obteniendo", simbolo, "...")
  
  tryCatch({
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = Sys.Date() - dias,
                       to = Sys.Date(),
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    # Convertir a dataframe
    df <- data.frame(
      Date = index(datos),
      Open = as.numeric(Op(datos)),
      High = as.numeric(Hi(datos)),
      Low = as.numeric(Lo(datos)),
      Close = as.numeric(Cl(datos)),
      Volume = as.numeric(Vo(datos))
    )
    
    # Limpiar datos
    df <- na.omit(df)
    
    if (nrow(df) < 20) {
      cat(" ❌ Datos insuficientes\n")
      return(NULL)
    }
    
    cat(" ✅", nrow(df), "días\n")
    return(df)
    
  }, error = function(e) {
    cat(" ❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# ANÁLISIS TÉCNICO COMPLETO
# =============================================================================

analizar_tecnico_completo <- function(datos) {
  if (is.null(datos) || nrow(datos) < 25) {
    return(list(señal = "SIN_DATOS", fuerza = 0))
  }
  
  # Ordenar por fecha
  datos <- datos[order(datos$Date), ]
  
  # Calcular indicadores técnicos
  datos$EMA_Rapida <- EMA(datos$Close, n = SCALPING_CONFIG$ema_rapida)
  datos$EMA_Lenta <- EMA(datos$Close, n = SCALPING_CONFIG$ema_lenta)
  datos$RSI <- RSI(datos$Close, n = SCALPING_CONFIG$rsi_periodo)
  
  # Bandas de Bollinger
  bb <- BBands(datos$Close, n = SCALPING_CONFIG$bb_periodo, sd = SCALPING_CONFIG$bb_desviaciones)
  datos$BB_Upper <- bb[, "up"]
  datos$BB_Middle <- bb[, "mavg"]
  datos$BB_Lower <- bb[, "dn"]
  
  # Calcular volatilidad
  datos$Volatilidad <- runSD(datos$Close, n = 10) / datos$Close * 100
  
  # Momentum
  datos$Momentum <- c(NA, diff(datos$Close))
  
  # Análisis de los últimos datos
  ultimo <- tail(datos, 1)
  anterior <- tail(datos, 2)[1, ]
  
  # Verificar datos válidos
  if (any(is.na(c(ultimo$EMA_Rapida, ultimo$EMA_Lenta, ultimo$RSI)))) {
    return(list(señal = "DATOS_INVALIDOS", fuerza = 0))
  }
  
  señal <- "NEUTRAL"
  fuerza <- 0
  razones <- c()
  confirmaciones <- 0
  
  # =============================================================================
  # ANÁLISIS 1: CRUCE DE EMAS
  # =============================================================================
  
  if (ultimo$EMA_Rapida > ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida <= anterior$EMA_Lenta) {
      # Cruce alcista reciente
      señal <- "COMPRA"
      fuerza <- fuerza + 3
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Cruce EMA alcista")
    } else if (ultimo$EMA_Rapida > anterior$EMA_Rapida) {
      # Tendencia alcista acelerándose
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Tendencia EMA alcista fuerte")
    } else {
      # Tendencia alcista establecida
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA alcista")
    }
  } else if (ultimo$EMA_Rapida < ultimo$EMA_Lenta) {
    if (anterior$EMA_Rapida >= anterior$EMA_Lenta) {
      # Cruce bajista reciente
      señal <- "VENTA"
      fuerza <- fuerza + 3
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Cruce EMA bajista")
    } else if (ultimo$EMA_Rapida < anterior$EMA_Rapida) {
      # Tendencia bajista acelerándose
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Tendencia EMA bajista fuerte")
    } else {
      # Tendencia bajista establecida
      fuerza <- fuerza + 1
      razones <- c(razones, "Tendencia EMA bajista")
    }
  }
  
  # =============================================================================
  # ANÁLISIS 2: RSI
  # =============================================================================
  
  if (ultimo$RSI < SCALPING_CONFIG$rsi_sobreventa) {
    if (ultimo$RSI > anterior$RSI) {
      # RSI sobreventa recuperándose
      if (señal == "NEUTRAL") señal <- "COMPRA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "RSI sobreventa recuperándose")
    } else {
      razones <- c(razones, "RSI sobreventa")
    }
  } else if (ultimo$RSI > SCALPING_CONFIG$rsi_sobrecompra) {
    if (ultimo$RSI < anterior$RSI) {
      # RSI sobrecompra corrigiéndose
      if (señal == "NEUTRAL") señal <- "VENTA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "RSI sobrecompra corrigiéndose")
    } else {
      razones <- c(razones, "RSI sobrecompra")
    }
  } else if (ultimo$RSI >= 40 && ultimo$RSI <= 60) {
    # RSI en zona neutral con momentum
    if (ultimo$RSI > anterior$RSI && señal == "COMPRA") {
      fuerza <- fuerza + 1
      razones <- c(razones, "RSI momentum alcista")
    } else if (ultimo$RSI < anterior$RSI && señal == "VENTA") {
      fuerza <- fuerza + 1
      razones <- c(razones, "RSI momentum bajista")
    }
  }
  
  # =============================================================================
  # ANÁLISIS 3: BOLLINGER BANDS
  # =============================================================================
  
  bb_position <- (ultimo$Close - ultimo$BB_Lower) / (ultimo$BB_Upper - ultimo$BB_Lower)
  
  if (ultimo$Close <= ultimo$BB_Lower) {
    # Precio en banda inferior
    if (ultimo$Momentum > 0) {
      # Rebote desde banda inferior
      if (señal == "NEUTRAL") señal <- "COMPRA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Rebote en banda inferior")
    } else {
      razones <- c(razones, "Precio en banda inferior")
    }
  } else if (ultimo$Close >= ultimo$BB_Upper) {
    # Precio en banda superior
    if (ultimo$Momentum < 0) {
      # Rechazo desde banda superior
      if (señal == "NEUTRAL") señal <- "VENTA"
      fuerza <- fuerza + 2
      confirmaciones <- confirmaciones + 1
      razones <- c(razones, "Rechazo en banda superior")
    } else {
      razones <- c(razones, "Precio en banda superior")
    }
  } else if (bb_position < 0.2 && ultimo$Momentum > 0) {
    # Cerca de banda inferior con momentum alcista
    fuerza <- fuerza + 1
    razones <- c(razones, "Cerca banda inferior con momentum")
  } else if (bb_position > 0.8 && ultimo$Momentum < 0) {
    # Cerca de banda superior con momentum bajista
    fuerza <- fuerza + 1
    razones <- c(razones, "Cerca banda superior con momentum")
  }
  
  # =============================================================================
  # ANÁLISIS 4: VOLATILIDAD
  # =============================================================================
  
  volatilidad_actual <- ultimo$Volatilidad
  if (!is.na(volatilidad_actual) && volatilidad_actual < SCALPING_CONFIG$min_volatilidad) {
    # Volatilidad muy baja, reducir fuerza
    fuerza <- max(0, fuerza - 1)
    razones <- c(razones, "Volatilidad baja")
  }
  
  return(list(
    señal = señal,
    fuerza = fuerza,
    confirmaciones = confirmaciones,
    precio = ultimo$Close,
    rsi = ultimo$RSI,
    ema_rapida = ultimo$EMA_Rapida,
    ema_lenta = ultimo$EMA_Lenta,
    bb_position = bb_position,
    volatilidad = volatilidad_actual,
    momentum = ultimo$Momentum,
    fecha = ultimo$Date,
    razones = razones
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ANÁLISIS
# =============================================================================

analisis_scalping_yahoo <- function(incluir_secundarios = FALSE) {
  cat("🎯 ANÁLISIS SCALPING YAHOO FINANCE\n")
  cat("==================================\n")
  cat("Hora:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")
  
  # Seleccionar pares a analizar
  pares_analizar <- SCALPING_CONFIG$pares_principales
  if (incluir_secundarios) {
    pares_analizar <- c(pares_analizar, SCALPING_CONFIG$pares_secundarios)
  }
  
  oportunidades <- list()
  
  for (par_nombre in names(pares_analizar)) {
    simbolo <- pares_analizar[[par_nombre]]
    
    cat("📊", par_nombre, "\n")
    cat(paste(rep("-", 30), collapse = ""), "\n")
    
    # Obtener datos
    datos <- obtener_datos_yahoo(simbolo)
    
    if (!is.null(datos)) {
      # Analizar técnicamente
      analisis <- analizar_tecnico_completo(datos)
      
      cat("💰 Precio actual:", sprintf("%.5f", analisis$precio), "\n")
      cat("🔍 Señal:", analisis$señal, "\n")
      cat("💪 Fuerza:", analisis$fuerza, "\n")
      cat("🎯 Confirmaciones:", analisis$confirmaciones, "\n")
      cat("📊 RSI:", sprintf("%.2f", analisis$rsi), "\n")
      
      if (!is.na(analisis$volatilidad)) {
        cat("📈 Volatilidad:", sprintf("%.3f%%", analisis$volatilidad), "\n")
      }
      
      if (length(analisis$razones) > 0) {
        cat("📋 Razones:\n")
        for (razon in analisis$razones) {
          cat("   •", razon, "\n")
        }
      }
      
      # Guardar oportunidades fuertes
      if (analisis$fuerza >= SCALPING_CONFIG$min_fuerza_señal && 
          analisis$señal %in% c("COMPRA", "VENTA")) {
        oportunidades[[par_nombre]] <- analisis
        cat("⭐ OPORTUNIDAD DETECTADA!\n")
      }
    }
    
    cat("\n")
  }
  
  # Generar guía XTB si hay oportunidades
  if (length(oportunidades) > 0) {
    generar_guia_scalping_xtb(oportunidades)
  } else {
    cat("😴 No hay oportunidades de scalping en este momento\n")
    cat("💡 Recomendación:\n")
    cat("   • Revisar en 2-4 horas\n")
    cat("   • Verificar horarios de mayor volatilidad\n")
    cat("   • Considerar pares secundarios: analisis_scalping_yahoo(TRUE)\n")
  }
  
  return(oportunidades)
}

# =============================================================================
# GUÍA COMPLETA PARA XTB
# =============================================================================

generar_guia_scalping_xtb <- function(oportunidades) {
  cat(paste(rep("=", 60), collapse = ""), "\n")
  cat("🎯 GUÍA SCALPING PARA XTB\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  # Ordenar por fuerza de señal
  oportunidades_ordenadas <- oportunidades[order(
    sapply(oportunidades, function(x) x$fuerza), 
    decreasing = TRUE
  )]
  
  cat("🚨 OPORTUNIDADES DE SCALPING:", length(oportunidades_ordenadas), "\n\n")
  
  operacion_num <- 1
  
  for (par in names(oportunidades_ordenadas)) {
    opp <- oportunidades_ordenadas[[par]]
    precio <- opp$precio
    
    cat("🎯 OPERACIÓN", operacion_num, ":", par, "\n")
    cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
    
    # Determinar tipo de operación y calcular niveles
    if (opp$señal == "COMPRA") {
      tipo <- "COMPRAR"
      color <- "VERDE"
      direccion <- "alcista"
      
      # Calcular niveles para compra
      if (grepl("JPY", par)) {
        # Para pares con JPY (pip = 0.01)
        sl <- precio - (SCALPING_CONFIG$stop_loss_pips * 0.01)
        tp <- precio + (SCALPING_CONFIG$take_profit_pips * 0.01)
      } else {
        # Para otros pares (pip = 0.0001)
        sl <- precio - (SCALPING_CONFIG$stop_loss_pips * 0.0001)
        tp <- precio + (SCALPING_CONFIG$take_profit_pips * 0.0001)
      }
      
    } else {  # VENTA
      tipo <- "VENDER"
      color <- "ROJO"
      direccion <- "bajista"
      
      # Calcular niveles para venta
      if (grepl("JPY", par)) {
        sl <- precio + (SCALPING_CONFIG$stop_loss_pips * 0.01)
        tp <- precio - (SCALPING_CONFIG$take_profit_pips * 0.01)
      } else {
        sl <- precio + (SCALPING_CONFIG$stop_loss_pips * 0.0001)
        tp <- precio - (SCALPING_CONFIG$take_profit_pips * 0.0001)
      }
    }
    
    cat("📊 ANÁLISIS TÉCNICO:\n")
    cat("   • Señal:", opp$señal, "(Fuerza:", opp$fuerza, ")\n")
    cat("   • Confirmaciones:", opp$confirmaciones, "\n")
    cat("   • Precio actual:", sprintf("%.5f", precio), "\n")
    cat("   • RSI:", sprintf("%.2f", opp$rsi), "\n")
    cat("   • Tendencia:", direccion, "\n")
    cat("   • Razones:", paste(head(opp$razones, 3), collapse = ", "), "\n\n")
    
    cat("🎯 INSTRUCCIONES PARA XTB:\n")
    cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
    cat("1️⃣ BUSCAR PAR:\n")
    cat("   • En XTB busca:", par, "\n")
    cat("   • Selecciona el par de divisas\n\n")
    
    cat("2️⃣ ABRIR OPERACIÓN:\n")
    cat("   • Clic en botón", color, ":", tipo, "\n")
    cat("   • Verificar que dice", tipo, par, "\n\n")
    
    cat("3️⃣ CONFIGURAR VOLUMEN:\n")
    cat("   • Volumen:", SCALPING_CONFIG$volumen_demo, "lotes\n")
    cat("   • (Para cuenta demo $", SCALPING_CONFIG$capital_demo, ")\n\n")
    
    cat("4️⃣ CONFIGURAR STOP LOSS:\n")
    cat("   • Stop Loss:", sprintf("%.5f", sl), "\n")
    cat("   • (", SCALPING_CONFIG$stop_loss_pips, "pips de protección)\n\n")
    
    cat("5️⃣ CONFIGURAR TAKE PROFIT:\n")
    cat("   • Take Profit:", sprintf("%.5f", tp), "\n")
    cat("   • (", SCALPING_CONFIG$take_profit_pips, "pips de ganancia)\n\n")
    
    cat("6️⃣ VERIFICAR Y EJECUTAR:\n")
    cat("   • Revisar todos los valores\n")
    cat("   • Clic en 'ABRIR OPERACIÓN'\n")
    cat("   • ✅ Operación ejecutada\n\n")
    
    # Calcular gestión de riesgo
    riesgo_euros <- SCALPING_CONFIG$capital_demo * (SCALPING_CONFIG$riesgo_por_operacion_pct / 100)
    ganancia_euros <- riesgo_euros * SCALPING_CONFIG$reward_risk_ratio
    
    cat("💰 GESTIÓN DE RIESGO:\n")
    cat("   • Riesgo máximo: $", round(riesgo_euros, 0), "\n")
    cat("   • Ganancia potencial: $", round(ganancia_euros, 0), "\n")
    cat("   • Ratio Risk:Reward = 1:", SCALPING_CONFIG$reward_risk_ratio, " ✅\n\n")
    
    cat("📱 MONITOREO POST-OPERACIÓN:\n")
    cat("   • Abrir gráfico de", par, "en timeframe 15min\n")
    cat("   • Verificar que el movimiento continúe", direccion, "\n")
    cat("   • Si hay reversión fuerte, considerar cierre manual\n")
    cat("   • Seguir noticias económicas relevantes\n\n")
    
    operacion_num <- operacion_num + 1
    
    # Límite de operaciones simultáneas
    if (operacion_num > SCALPING_CONFIG$max_operaciones_simultaneas) {
      cat("⚠️ LÍMITE DE OPERACIONES ALCANZADO\n")
      cat("💡 Máximo", SCALPING_CONFIG$max_operaciones_simultaneas, "operaciones simultáneas para gestión de riesgo\n")
      break
    }
  }
  
  # Verificaciones finales
  cat("📋 VERIFICACIONES FINALES:\n")
  cat("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n")
  cat("   ✅ Confirmar que es cuenta DEMO\n")
  cat("   ✅ Verificar dirección (COMPRAR/VENDER)\n")
  cat("   ✅ Confirmar volumen", SCALPING_CONFIG$volumen_demo, "lotes\n")
  cat("   ✅ Verificar Stop Loss y Take Profit\n")
  cat("   ✅ Máximo", SCALPING_CONFIG$max_operaciones_simultaneas, "operaciones abiertas\n")
  cat("   ✅ Monitorear gráficos después de abrir\n\n")
  
  cat("🚨 ALERTAS DE SEGURIDAD:\n")
  cat("   • NO operar con dinero real hasta dominar la estrategia\n")
  cat("   • Practicar en demo al menos 1 mes\n")
  cat("   • Cerrar operaciones antes de noticias importantes\n")
  cat("   • Nunca arriesgar más del 2% por operación\n\n")
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📖 INSTRUCCIONES DE USO:\n")
cat("========================\n")
cat("• Análisis principal: analisis_scalping_yahoo()\n")
cat("• Incluir pares secundarios: analisis_scalping_yahoo(TRUE)\n")
cat("• Sin límites de API - Usar libremente\n")
cat("• Ejecutar cada 2-4 horas para mejores resultados\n\n")

cat("🎯 EJEMPLO DE USO:\n")
cat("oportunidades <- analisis_scalping_yahoo()\n\n")

cat("💡 CONSEJOS:\n")
cat("• Mejor volatilidad: 8:00-12:00 y 14:00-18:00 UTC\n")
cat("• Evitar viernes tarde y domingos\n")
cat("• Combinar con análisis de noticias\n")
cat("• Practicar gestión emocional\n\n")

# =============================================================================
# FUNCIÓN RÁPIDA PARA MONITOREO EXPRESS
# =============================================================================

scalping_express <- function() {
  cat("⚡ SCALPING EXPRESS\n")
  cat("==================\n")

  oportunidades <- analisis_scalping_yahoo()

  if (length(oportunidades) > 0) {
    cat("\n🎯 RESUMEN RÁPIDO:\n")
    for (par in names(oportunidades)) {
      opp <- oportunidades[[par]]
      cat("🟢", par, "-", opp$señal, "- Fuerza:", opp$fuerza, "\n")
    }
  }

  return(oportunidades)
}
